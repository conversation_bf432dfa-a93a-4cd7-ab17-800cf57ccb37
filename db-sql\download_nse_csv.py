import requests
import pandas as pd
import io
import sqlite3
from datetime import date

# URL for NSE Bhavcopy
CSV_URL = 'https://archives.nseindia.com/products/content/sec_bhavdata_full.csv'


def download_and_update_db():
    try:
        print(f"Fetching data from {CSV_URL}...")
        response = requests.get(CSV_URL, timeout=30, headers={"User-Agent": "Mozilla/5.0"})

        if response.status_code == 200:
            df = pd.read_csv(io.BytesIO(response.content))
            print("Data fetched successfully.")
            update_database(df)
        else:
            raise Exception(f"Failed to download CSV file, status code: {response.status_code}")

    except Exception as e:
        print(f"An error occurred: {e}")


def update_database(df):
    conn = sqlite3.connect('tradable_stocks.db')
    cursor = conn.cursor()

    df.columns = map(str.lower, df.columns)  # Normalize column names
    df.to_sql('tradable_stocks', conn, if_exists='replace', index=False)
    conn.commit()
    conn.close()
    print("Database updated with new data.")


def main():
    download_and_update_db()


if __name__ == "__main__":
    main()
