import sqlite3
import os
import platform
from tabulate import tabulate
from datetime import datetime

def clear_screen():
    """Clear the console screen"""
    if platform.system() == "Windows":
        os.system('cls')
    else:
        os.system('clear')

def print_header():
    """Print the application header"""
    print("=" * 60)
    print("       TRADABLE STOCKS DATABASE QUERY INTERFACE")
    print("=" * 60)
    print()

def print_menu():
    """Display the main menu"""
    print("\n--- MENU OPTIONS ---")
    print("1. Show all stocks")
    print("2. Search stocks by symbol")
    print("3. Search stocks by industry")
    print("4. Show database schema")
    print("5. Execute custom SQL query")
    print("6. Count total stocks")
    print("7. Exit")
    print("-" * 20)

def execute_query(conn, query, params=None):
    """Execute a query and return results with column headers"""
    cursor = conn.cursor()
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        results = cursor.fetchall()
        if results:
            headers = [desc[0] for desc in cursor.description]
            return headers, results
        else:
            return None, []
    except sqlite3.Error as e:
        print(f"\nError executing query: {e}")
        return None, None

def show_all_stocks(conn):
    """Display all stocks in the database"""
    query = "SELECT * FROM tradable_stocks"
    headers, results = execute_query(conn, query)
    
    if results:
        print("\n=== ALL STOCKS ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nTotal records: {len(results)}")
    else:
        print("\nNo stocks found in the database.")

def search_by_symbol(conn):
    """Search stocks by symbol"""
    symbol = input("\nEnter stock symbol (or partial symbol): ").strip().upper()
    query = "SELECT * FROM tradable_stocks WHERE symbol LIKE ?"
    headers, results = execute_query(conn, query, (f"%{symbol}%",))
    
    if results:
        print(f"\n=== STOCKS MATCHING '{symbol}' ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nFound {len(results)} matching record(s)")
    else:
        print(f"\nNo stocks found matching '{symbol}'")

def search_by_industry(conn):
    """Search stocks by industry"""
    industry = input("\nEnter industry name (or partial name): ").strip()
    query = "SELECT * FROM tradable_stocks WHERE industry LIKE ?"
    headers, results = execute_query(conn, query, (f"%{industry}%",))
    
    if results:
        print(f"\n=== STOCKS IN '{industry}' INDUSTRY ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nFound {len(results)} matching record(s)")
    else:
        print(f"\nNo stocks found in '{industry}' industry")

def show_schema(conn):
    """Display the database schema"""
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(tradable_stocks)")
    schema_info = cursor.fetchall()
    
    print("\n=== DATABASE SCHEMA ===")
    print("\nTable: tradable_stocks")
    print("-" * 50)
    
    headers = ["Column ID", "Name", "Type", "Not Null", "Default", "Primary Key"]
    print(tabulate(schema_info, headers=headers, tablefmt="grid"))

def execute_custom_query(conn):
    """Execute a custom SQL query"""
    print("\n=== CUSTOM SQL QUERY ===")
    print("Enter your SQL query (type 'cancel' to return to menu):")
    query = input("> ").strip()
    
    if query.lower() == 'cancel':
        return
    
    headers, results = execute_query(conn, query)
    
    if headers and results:
        print("\nQuery Results:")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nReturned {len(results)} row(s)")
    elif results is not None:
        print("\nQuery executed successfully with no results.")

def count_stocks(conn):
    """Count total stocks in the database"""
    query = "SELECT COUNT(*) as total_stocks FROM tradable_stocks"
    headers, results = execute_query(conn, query)
    
    if results:
        total = results[0][0]
        print(f"\n=== TOTAL STOCKS IN DATABASE: {total} ===")
    
    # Also show count by industry
    query = """
        SELECT industry, COUNT(*) as count 
        FROM tradable_stocks 
        GROUP BY industry 
        ORDER BY count DESC
    """
    headers, results = execute_query(conn, query)
    
    if results:
        print("\n=== STOCKS BY INDUSTRY ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))

def main():
    """Main application loop"""
    db_path = "tradable_stocks.db"
    
    try:
        # Check if database exists
        if not os.path.exists(db_path):
            print(f"Error: Database '{db_path}' not found!")
            print("Please ensure the database file exists in the current directory.")
            input("\nPress Enter to exit...")
            return
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        
        while True:
            clear_screen()
            print_header()
            print_menu()
            
            choice = input("\nEnter your choice (1-7): ").strip()
            
            if choice == '1':
                show_all_stocks(conn)
            elif choice == '2':
                search_by_symbol(conn)
            elif choice == '3':
                search_by_industry(conn)
            elif choice == '4':
                show_schema(conn)
            elif choice == '5':
                execute_custom_query(conn)
            elif choice == '6':
                count_stocks(conn)
            elif choice == '7':
                print("\nThank you for using the Tradable Stocks Database Interface!")
                print("Goodbye!")
                break
            else:
                print("\nInvalid choice! Please select a number between 1 and 7.")
            
            if choice != '7':
                input("\nPress Enter to continue...")
        
    except Exception as e:
        print(f"\nFatal error: {e}")
        input("\nPress Enter to exit...")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()
