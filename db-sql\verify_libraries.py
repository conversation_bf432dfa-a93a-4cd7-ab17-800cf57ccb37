#!/usr/bin/env python3
"""
Quick verification script to test that all required libraries are working properly.
"""

print("=== Library Verification Test ===")

# Test pandas
try:
    import pandas as pd
    print(f"✓ pandas {pd.__version__} - Creating test DataFrame...")
    df = pd.DataFrame({'test': [1, 2, 3]})
    print(f"  DataFrame created successfully with shape: {df.shape}")
except Exception as e:
    print(f"✗ pandas test failed: {e}")

# Test requests
try:
    import requests
    print(f"✓ requests {requests.__version__} - Testing HTTP functionality...")
    # Test with a simple HEAD request to check connectivity (safe and fast)
    response = requests.head('https://httpbin.org/status/200', timeout=5)
    print(f"  HTTP test successful - Status code: {response.status_code}")
except Exception as e:
    print(f"✗ requests test failed: {e}")

# Test sqlite3
try:
    import sqlite3
    print(f"✓ sqlite3 available - Testing database functionality...")
    # Create in-memory database for testing
    conn = sqlite3.connect(':memory:')
    cursor = conn.cursor()
    cursor.execute('CREATE TABLE test (id INTEGER, name TEXT)')
    cursor.execute('INSERT INTO test VALUES (1, "test")')
    result = cursor.fetchone()
    conn.close()
    print(f"  Database test successful - SQLite version: {sqlite3.sqlite_version}")
except Exception as e:
    print(f"✗ sqlite3 test failed: {e}")

print("\n=== Verification Complete ===")
print("All required libraries are installed and functional!")
