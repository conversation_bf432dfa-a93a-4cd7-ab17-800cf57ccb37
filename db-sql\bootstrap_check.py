#!/usr/bin/env python3
"""
Bootstrap script to check for required Python libraries and install missing ones.
Checks for: pandas, requests, sqlite3
"""

import sys
import subprocess
import importlib.util

def check_library(library_name):
    """Check if a library is installed and return its version if available."""
    try:
        spec = importlib.util.find_spec(library_name)
        if spec is not None:
            module = importlib.import_module(library_name)
            version = getattr(module, '__version__', 'Version unknown')
            return True, version
        else:
            return False, None
    except ImportError:
        return False, None

def install_library(library_name):
    """Install a library using pip."""
    print(f"Installing {library_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", library_name])
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing {library_name}: {e}")
        return False

def main():
    print("=== Python Library Bootstrap Check ===")
    print(f"Python version: {sys.version}")
    print()
    
    # Libraries to check
    libraries_to_check = ['pandas', 'requests', 'sqlite3']
    libraries_to_install = ['pandas', 'requests']  # sqlite3 comes with CPython
    
    missing_libraries = []
    
    # Check each library
    for lib in libraries_to_check:
        is_available, version = check_library(lib)
        if is_available:
            print(f"✓ {lib} is available (version: {version})")
        else:
            print(f"✗ {lib} is missing")
            if lib in libraries_to_install:
                missing_libraries.append(lib)
    
    print()
    
    # Install missing libraries
    if missing_libraries:
        print(f"Installing missing libraries: {', '.join(missing_libraries)}")
        for lib in missing_libraries:
            success = install_library(lib)
            if not success:
                print(f"Failed to install {lib}")
                return False
        
        print("\nRe-checking installed libraries:")
        for lib in missing_libraries:
            is_available, version = check_library(lib)
            if is_available:
                print(f"✓ {lib} successfully installed (version: {version})")
            else:
                print(f"✗ {lib} installation failed")
    else:
        print("All required libraries are already available!")
    
    print("\n=== Final Library Status ===")
    for lib in libraries_to_check:
        is_available, version = check_library(lib)
        status = "✓" if is_available else "✗"
        print(f"{status} {lib}: {version if is_available else 'Not available'}")
    
    print("\nBootstrap check completed!")
    return True

if __name__ == "__main__":
    main()
