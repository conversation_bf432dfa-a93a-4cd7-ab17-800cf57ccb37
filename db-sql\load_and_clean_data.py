import pandas as pd
import re
from pathlib import Path

def normalize_column_name(col_name):
    """Convert column name to snake_case format"""
    # Strip whitespace
    col_name = col_name.strip()
    # Replace spaces and special characters with underscores
    col_name = re.sub(r'[\s\-\.\&]+', '_', col_name)
    # Convert to lowercase
    col_name = col_name.lower()
    # Remove multiple underscores
    col_name = re.sub(r'_+', '_', col_name)
    # Remove leading/trailing underscores
    col_name = col_name.strip('_')
    return col_name

def load_and_clean_data(csv_path):
    """
    Load CSV data into DataFrame and clean it according to specifications:
    - Strip column names and normalize to snake_case
    - Convert DATE_OF_LISTING to datetime64[ns] if present
    - Print shape and head to confirm load
    """
    
    # Check if file exists
    if not Path(csv_path).exists():
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    print(f"Loading CSV file: {csv_path}")
    
    # Load the CSV into DataFrame
    df = pd.read_csv(csv_path)
    
    # Strip and normalize column names to snake_case
    original_columns = df.columns.tolist()
    df.columns = [normalize_column_name(col) for col in df.columns]
    
    print(f"Column name transformations:")
    for orig, new in zip(original_columns, df.columns):
        if orig != new:
            print(f"  '{orig}' -> '{new}'")
    
    # Convert DATE_OF_LISTING to datetime64[ns] if the column exists
    date_columns = [col for col in df.columns if 'date_of_listing' in col.lower()]
    if date_columns:
        for date_col in date_columns:
            print(f"Converting '{date_col}' to datetime64[ns]...")
            df[date_col] = pd.to_datetime(df[date_col])
            print(f"  Data type after conversion: {df[date_col].dtype}")
    else:
        print("No DATE_OF_LISTING column found - skipping date conversion")
    
    # Print shape and head to confirm load
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame info:")
    print(f"  Rows: {df.shape[0]}")
    print(f"  Columns: {df.shape[1]}")
    print(f"  Column names: {list(df.columns)}")
    
    print(f"\nDataFrame head:")
    print(df.head())
    
    print(f"\nData types:")
    print(df.dtypes)
    
    return df

if __name__ == "__main__":
    # Path to the CSV file
    csv_file_path = r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"
    
    try:
        # Load and clean the data
        df = load_and_clean_data(csv_file_path)
        
        # Additional info about the loaded data
        print(f"\nSummary:")
        print(f"Successfully loaded {df.shape[0]} rows and {df.shape[1]} columns")
        
        # If there are any missing values, report them
        missing_data = df.isnull().sum()
        if missing_data.any():
            print(f"\nMissing values per column:")
            print(missing_data[missing_data > 0])
        else:
            print(f"\nNo missing values found in the dataset")
            
    except Exception as e:
        print(f"Error loading data: {e}")
