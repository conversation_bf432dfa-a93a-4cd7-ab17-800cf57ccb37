"""
Data loading and cleaning module - now a wrapper around the unified data processor.
This module maintains backward compatibility while using the new modular architecture.
"""

from data_processor import DataProcessor
from utils import ensure_file_exists

def load_and_clean_data(csv_path):
    """
    Load CSV data into DataFrame and clean it according to specifications.
    This function now uses the unified DataProcessor for consistency.

    Args:
        csv_path (str): Path to the CSV file to load

    Returns:
        pd.DataFrame: Cleaned DataFrame

    Raises:
        FileNotFoundError: If the CSV file doesn't exist
    """
    # Ensure file exists
    ensure_file_exists(csv_path, "CSV file")

    # Initialize data processor
    data_processor = DataProcessor(verbose=True)

    # Load the CSV file
    df = data_processor.load_csv_from_file(csv_path)

    if df is None:
        raise RuntimeError(f"Failed to load CSV file: {csv_path}")

    # Clean the DataFrame
    cleaned_df = data_processor.clean_dataframe(df)

    # Print summary information
    data_processor.print_data_summary(cleaned_df, f"Loaded and Cleaned Data from {csv_path}")

    return cleaned_df

if __name__ == "__main__":
    # Path to the CSV file
    csv_file_path = r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"
    
    try:
        # Load and clean the data
        df = load_and_clean_data(csv_file_path)
        
        # Additional info about the loaded data
        print(f"\nSummary:")
        print(f"Successfully loaded {df.shape[0]} rows and {df.shape[1]} columns")
        
        # If there are any missing values, report them
        missing_data = df.isnull().sum()
        if missing_data.any():
            print(f"\nMissing values per column:")
            print(missing_data[missing_data > 0])
        else:
            print(f"\nNo missing values found in the dataset")
            
    except Exception as e:
        print(f"Error loading data: {e}")
