"""
SQL schema generation module - now uses the unified database manager.
This module maintains backward compatibility while using the new modular architecture.
"""

from data_processor import DataProcessor
from database_manager import DatabaseManager
from utils import print_section_header

def map_pandas_dtype_to_sqlite(dtype):
    """
    Map pandas dtypes to SQLite types
    """
    dtype_str = str(dtype).lower()
    
    # Integer types
    if 'int' in dtype_str:
        return 'INTEGER'
    
    # Float types
    elif 'float' in dtype_str:
        return 'REAL'
    
    # Boolean types
    elif 'bool' in dtype_str:
        return 'INTEGER'  # SQLite stores booleans as integers
    
    # Datetime types
    elif 'datetime' in dtype_str or 'timestamp' in dtype_str:
        return 'TEXT'  # Store as ISO format text
    
    # Object/string types and everything else
    else:
        return 'TEXT'

def generate_create_table_sql(df, table_name):
    """
    Generate CREATE TABLE SQL statement dynamically from DataFrame columns and dtypes
    """
    columns_sql = []
    
    print(f"Mapping pandas dtypes to SQLite types:")
    print("-" * 50)
    
    for column, dtype in df.dtypes.items():
        sqlite_type = map_pandas_dtype_to_sqlite(dtype)
        columns_sql.append(f"    {column} {sqlite_type}")
        print(f"{column:20} | {str(dtype):15} -> {sqlite_type}")
    
    # Join all column definitions
    columns_definition = ",\n".join(columns_sql)
    
    # Build the complete CREATE TABLE statement
    create_table_sql = f"""CREATE TABLE {table_name} (
{columns_definition}
);"""
    
    return create_table_sql

def create_database_table(df, table_name, db_path="tradable_stocks.db"):
    """
    Create SQLite database and execute CREATE TABLE statement
    """
    # Generate the CREATE TABLE SQL
    create_sql = generate_create_table_sql(df, table_name)
    
    print(f"\nGenerated CREATE TABLE statement:")
    print("=" * 60)
    print(create_sql)
    print("=" * 60)
    
    # Connect to SQLite database (creates file if it doesn't exist)
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Drop table if it exists (for clean slate)
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        print(f"\nDropped existing '{table_name}' table if it existed.")
        
        # Execute CREATE TABLE statement
        cursor.execute(create_sql)
        print(f"Successfully created '{table_name}' table in '{db_path}'")
        
        # Commit changes
        conn.commit()
        
        # Verify table creation
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        result = cursor.fetchone()
        
        if result:
            print(f"✓ Table '{table_name}' verified in database")
            
            # Get table schema info
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            
            print(f"\nTable schema verification:")
            print("-" * 50)
            for col_info in columns_info:
                col_id, col_name, col_type, not_null, default_val, pk = col_info
                print(f"{col_name:20} | {col_type}")
        else:
            print(f"✗ Failed to verify table '{table_name}' creation")
            
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()
            print(f"\nDatabase connection closed.")
    
    return True

def main():
    """
    Main function to load CSV data and create SQL schema
    """
    print("Step 6: Generate a matching SQL schema")
    print("=" * 50)
    
    # Try to load data from the sample CSV first
    csv_files = ["sample_with_dates.csv", r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"]
    
    df = None
    csv_used = None
    
    for csv_path in csv_files:
        if Path(csv_path).exists():
            print(f"Loading CSV file: {csv_path}")
            try:
                # Load the CSV into DataFrame
                df = pd.read_csv(csv_path)
                
                # Strip and normalize column names to snake_case
                original_columns = df.columns.tolist()
                df.columns = [normalize_column_name(col) for col in df.columns]
                
                print(f"Column name transformations:")
                for orig, new in zip(original_columns, df.columns):
                    if orig != new:
                        print(f"  '{orig}' -> '{new}'")
                
                # Convert date columns to datetime if they exist
                date_columns = [col for col in df.columns if 'date_of_listing' in col.lower()]
                if date_columns:
                    for date_col in date_columns:
                        print(f"Converting '{date_col}' to datetime64[ns]...")
                        df[date_col] = pd.to_datetime(df[date_col])
                        print(f"  Data type after conversion: {df[date_col].dtype}")
                
                csv_used = csv_path
                break
                
            except Exception as e:
                print(f"Error loading {csv_path}: {e}")
                continue
    
    if df is None:
        print("No valid CSV file found. Creating a sample DataFrame for demonstration...")
        # Create a sample DataFrame that matches the expected structure
        df = pd.DataFrame({
            'symbol': ['RELIANCE', 'TCS', 'HDFCBANK'],
            'company_name': ['Reliance Industries Ltd.', 'Tata Consultancy Services Ltd.', 'HDFC Bank Ltd.'],
            'industry': ['Oil Gas & Consumable Fuels', 'Information Technology', 'Financial Services'],
            'date_of_listing': pd.to_datetime(['1995-11-29', '2004-08-25', '1995-11-08'])
        })
    
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    print(f"\nDataFrame dtypes:")
    print(df.dtypes)
    print(f"\nDataFrame head:")
    print(df.head())
    
    # Generate the CREATE TABLE SQL using the new database manager
    db_manager = DatabaseManager(verbose=True)
    create_table_sql = db_manager.generate_create_table_sql(df)

    print(f"\n" + "="*60)
    print("GENERATED SQL SCHEMA:")
    print("="*60)
    print(create_table_sql)

    # Optionally create the database and table
    create_database = input("\nDo you want to create the database table? (y/n): ").lower().strip()
    if create_database == 'y':
        if db_manager.create_table_from_dataframe(df):
            print("✓ Database table created successfully!")
        else:
            print("✗ Failed to create database table")

    print(f"\n" + "="*60)
    print("Schema generation completed!")
    print("="*60)

if __name__ == "__main__":
    main()
