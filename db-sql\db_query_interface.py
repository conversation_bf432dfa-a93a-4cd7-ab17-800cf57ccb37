import sqlite3
import os
import platform
from tabulate import tabulate

def clear_screen():
    if platform.system() == "Windows":
        os.system('cls')
    else:
        os.system('clear')

def user_query_interface(db_path="tradable_stocks.db"):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        clear_screen()
        print("=== Tradable Stocks Database Query Interface ===")
        print("Type 'exit' to quit.")

        while True:
            query = input("\nEnter SQL query: ").strip()
            if query.lower() == 'exit':
                print("Exiting...")
                break

            try:
                cursor.execute(query)
                results = cursor.fetchall()

                if results:
                    print("\nResults:")
                    print(tabulate(results, headers=[desc[0] for desc in cursor.description], tablefmt="grid"))
                else:
                    print("\nQuery executed successfully with no results.")
            except sqlite3.Error as e:
                print(f"\nSQLite error: {e}")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    user_query_interface()
