import pandas as pd
import re

def normalize_to_snake_case(column_name):
    """Convert column name to snake_case"""
    # Strip whitespace first
    column_name = column_name.strip()
    # Replace spaces and special characters with underscores
    column_name = re.sub(r'[\s\-\.\&]+', '_', column_name)
    # Convert to lowercase
    column_name = column_name.lower()
    # Clean up multiple underscores
    column_name = re.sub(r'_+', '_', column_name)
    # Remove leading/trailing underscores
    column_name = column_name.strip('_')
    return column_name

print("=== TASK 4: Load and clean the data with pandas ===\n")

# Parse the CSV into a DataFrame
csv_path = "sample_with_dates.csv"
print(f"Loading CSV: {csv_path}")
df = pd.read_csv(csv_path)

print("Original columns:", list(df.columns))
print("Sample of original data:")
print(df.head(2))

print("\n--- Cleaning Process ---")

# Strip column names and normalize to snake_case
print("1. Stripping column names...")
df.columns = df.columns.str.strip()  # Strip whitespace from column names

print("2. Normalizing to snake_case...")
original_columns = df.columns.tolist()
df.columns = [normalize_to_snake_case(col) for col in df.columns]

print("Column transformations:")
for orig, new in zip(original_columns, df.columns):
    print(f"  '{orig}' -> '{new}'")

# Convert DATE_OF_LISTING to datetime64[ns]
print("\n3. Converting DATE_OF_LISTING to datetime64[ns]...")
if 'date_of_listing' in df.columns:
    print(f"Before conversion - dtype: {df['date_of_listing'].dtype}")
    print(f"Sample values: {df['date_of_listing'].head(2).tolist()}")
    
    df['date_of_listing'] = pd.to_datetime(df['date_of_listing'])
    
    print(f"After conversion - dtype: {df['date_of_listing'].dtype}")
    print(f"Sample values: {df['date_of_listing'].head(2).tolist()}")
else:
    print("DATE_OF_LISTING column not found!")

print("\n--- Results ---")
# Print df.shape and df.head() to confirm the load went as expected
print(f"df.shape: {df.shape}")
print(f"\ndf.head():")
print(df.head())

print(f"\nData types:")
print(df.dtypes)

print(f"\n=== Task 4 Complete ===")
print(f"✓ CSV parsed into DataFrame")
print(f"✓ Column names stripped and normalized to snake_case")
print(f"✓ DATE_OF_LISTING converted to datetime64[ns]")
print(f"✓ Shape and head displayed for confirmation")
