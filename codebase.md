# db-sql\bootstrap_check.py

```py
#!/usr/bin/env python3
"""
Bootstrap script to check for required Python libraries and install missing ones.
Checks for: pandas, requests, sqlite3
"""

import sys
import subprocess
import importlib.util

def check_library(library_name):
    """Check if a library is installed and return its version if available."""
    try:
        spec = importlib.util.find_spec(library_name)
        if spec is not None:
            module = importlib.import_module(library_name)
            version = getattr(module, '__version__', 'Version unknown')
            return True, version
        else:
            return False, None
    except ImportError:
        return False, None

def install_library(library_name):
    """Install a library using pip."""
    print(f"Installing {library_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", library_name])
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error installing {library_name}: {e}")
        return False

def main():
    print("=== Python Library Bootstrap Check ===")
    print(f"Python version: {sys.version}")
    print()
    
    # Libraries to check
    libraries_to_check = ['pandas', 'requests', 'sqlite3']
    libraries_to_install = ['pandas', 'requests']  # sqlite3 comes with CPython
    
    missing_libraries = []
    
    # Check each library
    for lib in libraries_to_check:
        is_available, version = check_library(lib)
        if is_available:
            print(f"✓ {lib} is available (version: {version})")
        else:
            print(f"✗ {lib} is missing")
            if lib in libraries_to_install:
                missing_libraries.append(lib)
    
    print()
    
    # Install missing libraries
    if missing_libraries:
        print(f"Installing missing libraries: {', '.join(missing_libraries)}")
        for lib in missing_libraries:
            success = install_library(lib)
            if not success:
                print(f"Failed to install {lib}")
                return False
        
        print("\nRe-checking installed libraries:")
        for lib in missing_libraries:
            is_available, version = check_library(lib)
            if is_available:
                print(f"✓ {lib} successfully installed (version: {version})")
            else:
                print(f"✗ {lib} installation failed")
    else:
        print("All required libraries are already available!")
    
    print("\n=== Final Library Status ===")
    for lib in libraries_to_check:
        is_available, version = check_library(lib)
        status = "✓" if is_available else "✗"
        print(f"{status} {lib}: {version if is_available else 'Not available'}")
    
    print("\nBootstrap check completed!")
    return True

if __name__ == "__main__":
    main()

```

# db-sql\complete_demo.py

```py
import pandas as pd
import re

def normalize_to_snake_case(column_name):
    """Convert column name to snake_case"""
    # Strip whitespace first
    column_name = column_name.strip()
    # Replace spaces and special characters with underscores
    column_name = re.sub(r'[\s\-\.\&]+', '_', column_name)
    # Convert to lowercase
    column_name = column_name.lower()
    # Clean up multiple underscores
    column_name = re.sub(r'_+', '_', column_name)
    # Remove leading/trailing underscores
    column_name = column_name.strip('_')
    return column_name

print("=== TASK 4: Load and clean the data with pandas ===\n")

# Parse the CSV into a DataFrame
csv_path = "sample_with_dates.csv"
print(f"Loading CSV: {csv_path}")
df = pd.read_csv(csv_path)

print("Original columns:", list(df.columns))
print("Sample of original data:")
print(df.head(2))

print("\n--- Cleaning Process ---")

# Strip column names and normalize to snake_case
print("1. Stripping column names...")
df.columns = df.columns.str.strip()  # Strip whitespace from column names

print("2. Normalizing to snake_case...")
original_columns = df.columns.tolist()
df.columns = [normalize_to_snake_case(col) for col in df.columns]

print("Column transformations:")
for orig, new in zip(original_columns, df.columns):
    print(f"  '{orig}' -> '{new}'")

# Convert DATE_OF_LISTING to datetime64[ns]
print("\n3. Converting DATE_OF_LISTING to datetime64[ns]...")
if 'date_of_listing' in df.columns:
    print(f"Before conversion - dtype: {df['date_of_listing'].dtype}")
    print(f"Sample values: {df['date_of_listing'].head(2).tolist()}")
    
    df['date_of_listing'] = pd.to_datetime(df['date_of_listing'])
    
    print(f"After conversion - dtype: {df['date_of_listing'].dtype}")
    print(f"Sample values: {df['date_of_listing'].head(2).tolist()}")
else:
    print("DATE_OF_LISTING column not found!")

print("\n--- Results ---")
# Print df.shape and df.head() to confirm the load went as expected
print(f"df.shape: {df.shape}")
print(f"\ndf.head():")
print(df.head())

print(f"\nData types:")
print(df.dtypes)

print(f"\n=== Task 4 Complete ===")
print(f"✓ CSV parsed into DataFrame")
print(f"✓ Column names stripped and normalized to snake_case")
print(f"✓ DATE_OF_LISTING converted to datetime64[ns]")
print(f"✓ Shape and head displayed for confirmation")

```

# db-sql\config.py

```py
# Configuration constants for NSE stocks data processing

CSV_URL = "https://archives.nseindia.com/content/equities/EQUITY_L.csv"
DB_FILE = "nse_stocks.db"
TABLE = "tradable_stocks"

```

# db-sql\database_setup.py

```py
import sqlite3
import pathlib
from config import DB_FILE

# Create / connect to the SQLite database
print(f"Connecting to database: {DB_FILE}")

# Open a connection
con = sqlite3.connect(DB_FILE)
cur = con.cursor()

print(f"Successfully connected to {DB_FILE}")

# Optionally remove any existing table
print("Removing existing 'tradable_stocks' table if it exists...")
cur.execute("DROP TABLE IF EXISTS tradable_stocks")

print("✓ Database connection established")
print("✓ Existing 'tradable_stocks' table dropped (if existed)")
print(f"✓ Database file: {pathlib.Path(DB_FILE).absolute()}")

# Commit the changes (for the DROP TABLE operation)
con.commit()

print("\nDatabase setup complete. Connection and cursor are ready for use.")
print("Variables available:")
print(f"  - con: sqlite3.Connection object")
print(f"  - cur: sqlite3.Cursor object")

```

# db-sql\db_connection_snippet.py

```py
# Database connection snippet - Step 5
# Copy this code block into your scripts where database connection is needed

import sqlite3, pathlib
from config import DB_FILE

# Open a connection
con = sqlite3.connect(DB_FILE)
cur = con.cursor()

# Optionally remove any existing table
cur.execute("DROP TABLE IF EXISTS tradable_stocks")
con.commit()

# Now con and cur are ready for use

```

# db-sql\db_interface_enhanced.py

```py
import sqlite3
import os
import platform
from tabulate import tabulate
from datetime import datetime

def clear_screen():
    """Clear the console screen"""
    if platform.system() == "Windows":
        os.system('cls')
    else:
        os.system('clear')

def print_header():
    """Print the application header"""
    print("=" * 60)
    print("       TRADABLE STOCKS DATABASE QUERY INTERFACE")
    print("=" * 60)
    print()

def print_menu():
    """Display the main menu"""
    print("\n--- MENU OPTIONS ---")
    print("1. Show all stocks")
    print("2. Search stocks by symbol")
    print("3. Search stocks by industry")
    print("4. Show database schema")
    print("5. Execute custom SQL query")
    print("6. Count total stocks")
    print("7. Exit")
    print("-" * 20)

def execute_query(conn, query, params=None):
    """Execute a query and return results with column headers"""
    cursor = conn.cursor()
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        results = cursor.fetchall()
        if results:
            headers = [desc[0] for desc in cursor.description]
            return headers, results
        else:
            return None, []
    except sqlite3.Error as e:
        print(f"\nError executing query: {e}")
        return None, None

def show_all_stocks(conn):
    """Display all stocks in the database"""
    query = "SELECT * FROM tradable_stocks"
    headers, results = execute_query(conn, query)
    
    if results:
        print("\n=== ALL STOCKS ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nTotal records: {len(results)}")
    else:
        print("\nNo stocks found in the database.")

def search_by_symbol(conn):
    """Search stocks by symbol"""
    symbol = input("\nEnter stock symbol (or partial symbol): ").strip().upper()
    query = "SELECT * FROM tradable_stocks WHERE symbol LIKE ?"
    headers, results = execute_query(conn, query, (f"%{symbol}%",))
    
    if results:
        print(f"\n=== STOCKS MATCHING '{symbol}' ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nFound {len(results)} matching record(s)")
    else:
        print(f"\nNo stocks found matching '{symbol}'")

def search_by_industry(conn):
    """Search stocks by industry"""
    industry = input("\nEnter industry name (or partial name): ").strip()
    query = "SELECT * FROM tradable_stocks WHERE industry LIKE ?"
    headers, results = execute_query(conn, query, (f"%{industry}%",))
    
    if results:
        print(f"\n=== STOCKS IN '{industry}' INDUSTRY ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nFound {len(results)} matching record(s)")
    else:
        print(f"\nNo stocks found in '{industry}' industry")

def show_schema(conn):
    """Display the database schema"""
    cursor = conn.cursor()
    cursor.execute("PRAGMA table_info(tradable_stocks)")
    schema_info = cursor.fetchall()
    
    print("\n=== DATABASE SCHEMA ===")
    print("\nTable: tradable_stocks")
    print("-" * 50)
    
    headers = ["Column ID", "Name", "Type", "Not Null", "Default", "Primary Key"]
    print(tabulate(schema_info, headers=headers, tablefmt="grid"))

def execute_custom_query(conn):
    """Execute a custom SQL query"""
    print("\n=== CUSTOM SQL QUERY ===")
    print("Enter your SQL query (type 'cancel' to return to menu):")
    query = input("> ").strip()
    
    if query.lower() == 'cancel':
        return
    
    headers, results = execute_query(conn, query)
    
    if headers and results:
        print("\nQuery Results:")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nReturned {len(results)} row(s)")
    elif results is not None:
        print("\nQuery executed successfully with no results.")

def count_stocks(conn):
    """Count total stocks in the database"""
    query = "SELECT COUNT(*) as total_stocks FROM tradable_stocks"
    headers, results = execute_query(conn, query)
    
    if results:
        total = results[0][0]
        print(f"\n=== TOTAL STOCKS IN DATABASE: {total} ===")
    
    # Also show count by industry
    query = """
        SELECT industry, COUNT(*) as count 
        FROM tradable_stocks 
        GROUP BY industry 
        ORDER BY count DESC
    """
    headers, results = execute_query(conn, query)
    
    if results:
        print("\n=== STOCKS BY INDUSTRY ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))

def main():
    """Main application loop"""
    db_path = "tradable_stocks.db"
    
    try:
        # Check if database exists
        if not os.path.exists(db_path):
            print(f"Error: Database '{db_path}' not found!")
            print("Please ensure the database file exists in the current directory.")
            input("\nPress Enter to exit...")
            return
        
        # Connect to database
        conn = sqlite3.connect(db_path)
        
        while True:
            clear_screen()
            print_header()
            print_menu()
            
            choice = input("\nEnter your choice (1-7): ").strip()
            
            if choice == '1':
                show_all_stocks(conn)
            elif choice == '2':
                search_by_symbol(conn)
            elif choice == '3':
                search_by_industry(conn)
            elif choice == '4':
                show_schema(conn)
            elif choice == '5':
                execute_custom_query(conn)
            elif choice == '6':
                count_stocks(conn)
            elif choice == '7':
                print("\nThank you for using the Tradable Stocks Database Interface!")
                print("Goodbye!")
                break
            else:
                print("\nInvalid choice! Please select a number between 1 and 7.")
            
            if choice != '7':
                input("\nPress Enter to continue...")
        
    except Exception as e:
        print(f"\nFatal error: {e}")
        input("\nPress Enter to exit...")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    main()

```

# db-sql\db_query_interface.py

```py
import sqlite3
import os
import platform
from tabulate import tabulate

def clear_screen():
    if platform.system() == "Windows":
        os.system('cls')
    else:
        os.system('clear')

def user_query_interface(db_path="tradable_stocks.db"):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        clear_screen()
        print("=== Tradable Stocks Database Query Interface ===")
        print("Type 'exit' to quit.")

        while True:
            query = input("\nEnter SQL query: ").strip()
            if query.lower() == 'exit':
                print("Exiting...")
                break

            try:
                cursor.execute(query)
                results = cursor.fetchall()

                if results:
                    print("\nResults:")
                    print(tabulate(results, headers=[desc[0] for desc in cursor.description], tablefmt="grid"))
                else:
                    print("\nQuery executed successfully with no results.")
            except sqlite3.Error as e:
                print(f"\nSQLite error: {e}")

    except Exception as e:
        print(f"Error: {e}")
    finally:
        if conn:
            conn.close()

if __name__ == "__main__":
    user_query_interface()

```

# db-sql\download_nse_csv.py

```py
import requests
import pandas as pd
import io
import sqlite3
from datetime import date

# URL for NSE Bhavcopy
CSV_URL = 'https://archives.nseindia.com/products/content/sec_bhavdata_full.csv'


def download_and_update_db():
    try:
        print(f"Fetching data from {CSV_URL}...")
        response = requests.get(CSV_URL, timeout=30, headers={"User-Agent": "Mozilla/5.0"})

        if response.status_code == 200:
            df = pd.read_csv(io.BytesIO(response.content))
            print("Data fetched successfully.")
            update_database(df)
        else:
            raise Exception(f"Failed to download CSV file, status code: {response.status_code}")

    except Exception as e:
        print(f"An error occurred: {e}")


def update_database(df):
    conn = sqlite3.connect('tradable_stocks.db')
    cursor = conn.cursor()

    df.columns = map(str.lower, df.columns)  # Normalize column names
    df.to_sql('tradable_stocks', conn, if_exists='replace', index=False)
    conn.commit()
    conn.close()
    print("Database updated with new data.")


def main():
    download_and_update_db()


if __name__ == "__main__":
    main()

```

# db-sql\generate_sql_schema.py

```py
import pandas as pd
import sqlite3
import re
from pathlib import Path

def normalize_column_name(col_name):
    """Convert column name to snake_case format"""
    # Strip whitespace
    col_name = col_name.strip()
    # Replace spaces and special characters with underscores
    col_name = re.sub(r'[\s\-\.\&]+', '_', col_name)
    # Convert to lowercase
    col_name = col_name.lower()
    # Remove multiple underscores
    col_name = re.sub(r'_+', '_', col_name)
    # Remove leading/trailing underscores
    col_name = col_name.strip('_')
    return col_name

def map_pandas_dtype_to_sqlite(dtype):
    """
    Map pandas dtypes to SQLite types
    """
    dtype_str = str(dtype).lower()
    
    # Integer types
    if 'int' in dtype_str:
        return 'INTEGER'
    
    # Float types
    elif 'float' in dtype_str:
        return 'REAL'
    
    # Boolean types
    elif 'bool' in dtype_str:
        return 'INTEGER'  # SQLite stores booleans as integers
    
    # Datetime types
    elif 'datetime' in dtype_str or 'timestamp' in dtype_str:
        return 'TEXT'  # Store as ISO format text
    
    # Object/string types and everything else
    else:
        return 'TEXT'

def generate_create_table_sql(df, table_name):
    """
    Generate CREATE TABLE SQL statement dynamically from DataFrame columns and dtypes
    """
    columns_sql = []
    
    print(f"Mapping pandas dtypes to SQLite types:")
    print("-" * 50)
    
    for column, dtype in df.dtypes.items():
        sqlite_type = map_pandas_dtype_to_sqlite(dtype)
        columns_sql.append(f"    {column} {sqlite_type}")
        print(f"{column:20} | {str(dtype):15} -> {sqlite_type}")
    
    # Join all column definitions
    columns_definition = ",\n".join(columns_sql)
    
    # Build the complete CREATE TABLE statement
    create_table_sql = f"""CREATE TABLE {table_name} (
{columns_definition}
);"""
    
    return create_table_sql

def create_database_table(df, table_name, db_path="tradable_stocks.db"):
    """
    Create SQLite database and execute CREATE TABLE statement
    """
    # Generate the CREATE TABLE SQL
    create_sql = generate_create_table_sql(df, table_name)
    
    print(f"\nGenerated CREATE TABLE statement:")
    print("=" * 60)
    print(create_sql)
    print("=" * 60)
    
    # Connect to SQLite database (creates file if it doesn't exist)
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Drop table if it exists (for clean slate)
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        print(f"\nDropped existing '{table_name}' table if it existed.")
        
        # Execute CREATE TABLE statement
        cursor.execute(create_sql)
        print(f"Successfully created '{table_name}' table in '{db_path}'")
        
        # Commit changes
        conn.commit()
        
        # Verify table creation
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        result = cursor.fetchone()
        
        if result:
            print(f"✓ Table '{table_name}' verified in database")
            
            # Get table schema info
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            
            print(f"\nTable schema verification:")
            print("-" * 50)
            for col_info in columns_info:
                col_id, col_name, col_type, not_null, default_val, pk = col_info
                print(f"{col_name:20} | {col_type}")
        else:
            print(f"✗ Failed to verify table '{table_name}' creation")
            
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()
            print(f"\nDatabase connection closed.")
    
    return True

def main():
    """
    Main function to load CSV data and create SQL schema
    """
    print("Step 6: Generate a matching SQL schema")
    print("=" * 50)
    
    # Try to load data from the sample CSV first
    csv_files = ["sample_with_dates.csv", r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"]
    
    df = None
    csv_used = None
    
    for csv_path in csv_files:
        if Path(csv_path).exists():
            print(f"Loading CSV file: {csv_path}")
            try:
                # Load the CSV into DataFrame
                df = pd.read_csv(csv_path)
                
                # Strip and normalize column names to snake_case
                original_columns = df.columns.tolist()
                df.columns = [normalize_column_name(col) for col in df.columns]
                
                print(f"Column name transformations:")
                for orig, new in zip(original_columns, df.columns):
                    if orig != new:
                        print(f"  '{orig}' -> '{new}'")
                
                # Convert date columns to datetime if they exist
                date_columns = [col for col in df.columns if 'date_of_listing' in col.lower()]
                if date_columns:
                    for date_col in date_columns:
                        print(f"Converting '{date_col}' to datetime64[ns]...")
                        df[date_col] = pd.to_datetime(df[date_col])
                        print(f"  Data type after conversion: {df[date_col].dtype}")
                
                csv_used = csv_path
                break
                
            except Exception as e:
                print(f"Error loading {csv_path}: {e}")
                continue
    
    if df is None:
        print("No valid CSV file found. Creating a sample DataFrame for demonstration...")
        # Create a sample DataFrame that matches the expected structure
        df = pd.DataFrame({
            'symbol': ['RELIANCE', 'TCS', 'HDFCBANK'],
            'company_name': ['Reliance Industries Ltd.', 'Tata Consultancy Services Ltd.', 'HDFC Bank Ltd.'],
            'industry': ['Oil Gas & Consumable Fuels', 'Information Technology', 'Financial Services'],
            'date_of_listing': pd.to_datetime(['1995-11-29', '2004-08-25', '1995-11-08'])
        })
    
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    print(f"\nDataFrame dtypes:")
    print(df.dtypes)
    print(f"\nDataFrame head:")
    print(df.head())
    
    # Generate and execute CREATE TABLE statement
    table_name = "tradable_stocks"
    success = create_database_table(df, table_name)
    
    if success:
        print(f"\n✓ Successfully completed Step 6: Generated and executed SQL schema for '{table_name}' table")
    else:
        print(f"\n✗ Failed to complete Step 6")

if __name__ == "__main__":
    main()

```

# db-sql\load_and_clean_data.py

```py
import pandas as pd
import re
from pathlib import Path

def normalize_column_name(col_name):
    """Convert column name to snake_case format"""
    # Strip whitespace
    col_name = col_name.strip()
    # Replace spaces and special characters with underscores
    col_name = re.sub(r'[\s\-\.\&]+', '_', col_name)
    # Convert to lowercase
    col_name = col_name.lower()
    # Remove multiple underscores
    col_name = re.sub(r'_+', '_', col_name)
    # Remove leading/trailing underscores
    col_name = col_name.strip('_')
    return col_name

def load_and_clean_data(csv_path):
    """
    Load CSV data into DataFrame and clean it according to specifications:
    - Strip column names and normalize to snake_case
    - Convert DATE_OF_LISTING to datetime64[ns] if present
    - Print shape and head to confirm load
    """
    
    # Check if file exists
    if not Path(csv_path).exists():
        raise FileNotFoundError(f"CSV file not found: {csv_path}")
    
    print(f"Loading CSV file: {csv_path}")
    
    # Load the CSV into DataFrame
    df = pd.read_csv(csv_path)
    
    # Strip and normalize column names to snake_case
    original_columns = df.columns.tolist()
    df.columns = [normalize_column_name(col) for col in df.columns]
    
    print(f"Column name transformations:")
    for orig, new in zip(original_columns, df.columns):
        if orig != new:
            print(f"  '{orig}' -> '{new}'")
    
    # Convert DATE_OF_LISTING to datetime64[ns] if the column exists
    date_columns = [col for col in df.columns if 'date_of_listing' in col.lower()]
    if date_columns:
        for date_col in date_columns:
            print(f"Converting '{date_col}' to datetime64[ns]...")
            df[date_col] = pd.to_datetime(df[date_col])
            print(f"  Data type after conversion: {df[date_col].dtype}")
    else:
        print("No DATE_OF_LISTING column found - skipping date conversion")
    
    # Print shape and head to confirm load
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame info:")
    print(f"  Rows: {df.shape[0]}")
    print(f"  Columns: {df.shape[1]}")
    print(f"  Column names: {list(df.columns)}")
    
    print(f"\nDataFrame head:")
    print(df.head())
    
    print(f"\nData types:")
    print(df.dtypes)
    
    return df

if __name__ == "__main__":
    # Path to the CSV file
    csv_file_path = r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"
    
    try:
        # Load and clean the data
        df = load_and_clean_data(csv_file_path)
        
        # Additional info about the loaded data
        print(f"\nSummary:")
        print(f"Successfully loaded {df.shape[0]} rows and {df.shape[1]} columns")
        
        # If there are any missing values, report them
        missing_data = df.isnull().sum()
        if missing_data.any():
            print(f"\nMissing values per column:")
            print(missing_data[missing_data > 0])
        else:
            print(f"\nNo missing values found in the dataset")
            
    except Exception as e:
        print(f"Error loading data: {e}")

```

# db-sql\populate_database.py

```py
import pandas as pd
import sqlite3
import re
from pathlib import Path

def normalize_column_name(col_name):
    """Convert column name to snake_case format"""
    # Strip whitespace
    col_name = col_name.strip()
    # Replace spaces and special characters with underscores
    col_name = re.sub(r'[\s\-\.\&]+', '_', col_name)
    # Convert to lowercase
    col_name = col_name.lower()
    # Remove multiple underscores
    col_name = re.sub(r'_+', '_', col_name)
    # Remove leading/trailing underscores
    col_name = col_name.strip('_')
    return col_name

def populate_database():
    """Load CSV data and populate the tradable_stocks database"""
    print("=== Populating Tradable Stocks Database ===")
    print()
    
    # Try different CSV file locations
    csv_files = [
        "sample_with_dates.csv",
        r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv",
        r"C:\Users\<USER>\sample_with_dates.csv"
    ]
    
    df = None
    csv_used = None
    
    # Find and load CSV file
    for csv_path in csv_files:
        if Path(csv_path).exists():
            print(f"Found CSV file: {csv_path}")
            try:
                # Load the CSV into DataFrame
                df = pd.read_csv(csv_path)
                csv_used = csv_path
                break
            except Exception as e:
                print(f"Error loading {csv_path}: {e}")
                continue
    
    if df is None:
        print("No CSV file found. Cannot populate database.")
        return False
    
    print(f"Successfully loaded CSV from: {csv_used}")
    print(f"Original shape: {df.shape}")
    
    # Strip and normalize column names to snake_case
    original_columns = df.columns.tolist()
    df.columns = [normalize_column_name(col) for col in df.columns]
    
    print("\nColumn name transformations:")
    for orig, new in zip(original_columns, df.columns):
        if orig != new:
            print(f"  '{orig}' -> '{new}'")
    
    # Convert date columns to datetime if they exist
    date_columns = [col for col in df.columns if 'date_of_listing' in col.lower()]
    if date_columns:
        for date_col in date_columns:
            print(f"\nConverting '{date_col}' to datetime...")
            df[date_col] = pd.to_datetime(df[date_col])
            # Convert to string format for SQLite storage
            df[date_col] = df[date_col].dt.strftime('%Y-%m-%d')
    
    # Connect to database
    try:
        conn = sqlite3.connect('tradable_stocks.db')
        cursor = conn.cursor()
        
        # Clear existing data
        cursor.execute("DELETE FROM tradable_stocks")
        print("\nCleared existing data from tradable_stocks table")
        
        # Insert data into database
        print(f"\nInserting {len(df)} records into database...")
        
        # Use pandas to_sql for easy insertion
        df.to_sql('tradable_stocks', conn, if_exists='append', index=False)
        
        # Verify insertion
        cursor.execute("SELECT COUNT(*) FROM tradable_stocks")
        count = cursor.fetchone()[0]
        
        print(f"✓ Successfully inserted {count} records into database")
        
        # Show sample of inserted data
        cursor.execute("SELECT * FROM tradable_stocks LIMIT 5")
        sample_data = cursor.fetchall()
        
        print("\nSample of inserted data:")
        print("-" * 80)
        cursor.execute("PRAGMA table_info(tradable_stocks)")
        columns = [col[1] for col in cursor.fetchall()]
        
        for row in sample_data:
            for col_name, value in zip(columns, row):
                print(f"{col_name}: {value}")
            print("-" * 40)
        
        conn.commit()
        conn.close()
        
        print("\n✓ Database population completed successfully!")
        return True
        
    except Exception as e:
        print(f"\nError populating database: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    success = populate_database()
    if not success:
        print("\n✗ Failed to populate database")
    input("\nPress Enter to exit...")

```

# db-sql\requirements.txt

```txt
tabulate==0.9.0
pandas==2.1.4
requests==2.31.0

```

# db-sql\run_db_interface.bat

```bat
@echo off
title Tradable Stocks Database Interface
echo Fetching and updating latest stock data from NSE...
echo.
uv run python download_nse_csv.py
echo.
echo Launching Tradable Stocks Database Interface...
echo.
uv run python db_interface_enhanced.py
echo.
echo Application closed.
pause

```

# db-sql\run_db_query_interface.bat

```bat
@echo off
uv run python db_query_interface.py
pause

```

# db-sql\sample_with_dates.csv

```csv
Symbol, Company Name , Industry,DATE_OF_LISTING  
RELIANCE,Reliance Industries Ltd.,Oil Gas & Consumable Fuels,1995-11-29
TCS,Tata Consultancy Services Ltd.,Information Technology,2004-08-25
HDFCBANK,HDFC Bank Ltd.,Financial Services,1995-11-08
INFY,Infosys Ltd.,Information Technology,1993-06-12
ICICIBANK,ICICI Bank Ltd.,Financial Services,1997-09-17

```

# db-sql\task4_demo.py

```py
import pandas as pd
import re

def normalize_to_snake_case(column_name):
    """Convert column name to snake_case"""
    # Strip whitespace first
    column_name = column_name.strip()
    # Replace spaces and special characters with underscores
    column_name = re.sub(r'[\s\-\.\&]+', '_', column_name)
    # Convert to lowercase
    column_name = column_name.lower()
    # Clean up multiple underscores
    column_name = re.sub(r'_+', '_', column_name)
    # Remove leading/trailing underscores
    column_name = column_name.strip('_')
    return column_name

# Parse the CSV into a DataFrame
csv_path = r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"
df = pd.read_csv(csv_path)

# Strip column names and normalize to snake_case
df.columns = df.columns.str.strip()  # Strip whitespace from column names
df.columns = [normalize_to_snake_case(col) for col in df.columns]  # Normalize to snake_case

# Convert DATE_OF_LISTING to datetime64[ns] if it exists
# Note: The current CSV doesn't have this column, but this shows how it would be done
if 'date_of_listing' in df.columns:
    df['date_of_listing'] = pd.to_datetime(df['date_of_listing'])
    print(f"Converted date_of_listing to {df['date_of_listing'].dtype}")
else:
    print("DATE_OF_LISTING column not present in this dataset")

# Print df.shape and df.head() to confirm the load went as expected
print(f"df.shape: {df.shape}")
print(f"\ndf.head():")
print(df.head())

```

# db-sql\tradable_stocks.db

This is a binary file of the type: Binary

# db-sql\verify_libraries.py

```py
#!/usr/bin/env python3
"""
Quick verification script to test that all required libraries are working properly.
"""

print("=== Library Verification Test ===")

# Test pandas
try:
    import pandas as pd
    print(f"✓ pandas {pd.__version__} - Creating test DataFrame...")
    df = pd.DataFrame({'test': [1, 2, 3]})
    print(f"  DataFrame created successfully with shape: {df.shape}")
except Exception as e:
    print(f"✗ pandas test failed: {e}")

# Test requests
try:
    import requests
    print(f"✓ requests {requests.__version__} - Testing HTTP functionality...")
    # Test with a simple HEAD request to check connectivity (safe and fast)
    response = requests.head('https://httpbin.org/status/200', timeout=5)
    print(f"  HTTP test successful - Status code: {response.status_code}")
except Exception as e:
    print(f"✗ requests test failed: {e}")

# Test sqlite3
try:
    import sqlite3
    print(f"✓ sqlite3 available - Testing database functionality...")
    # Create in-memory database for testing
    conn = sqlite3.connect(':memory:')
    cursor = conn.cursor()
    cursor.execute('CREATE TABLE test (id INTEGER, name TEXT)')
    cursor.execute('INSERT INTO test VALUES (1, "test")')
    result = cursor.fetchone()
    conn.close()
    print(f"  Database test successful - SQLite version: {sqlite3.sqlite_version}")
except Exception as e:
    print(f"✗ sqlite3 test failed: {e}")

print("\n=== Verification Complete ===")
print("All required libraries are installed and functional!")

```

