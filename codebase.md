# db-sql\config.py

```py
"""
Configuration settings for the NSE stocks database application.
All configurable parameters should be defined here to maintain consistency across modules.
"""

from pathlib import Path

# Database configuration
DB_FILE = "tradable_stocks.db"
TABLE_NAME = "tradable_stocks"

# Data source URLs
# Primary URL for NSE equity list (more comprehensive data)
PRIMARY_CSV_URL = "https://archives.nseindia.com/content/equities/EQUITY_L.csv"
# Alternative URL for daily bhav data (simpler structure)
BHAV_CSV_URL = "https://archives.nseindia.com/products/content/sec_bhavdata_full.csv"

# Local CSV files to try (in order of preference)
LOCAL_CSV_FILES = [
    "sample_with_dates.csv",
    "nifty_500_stocks.csv"
]

# HTTP request configuration
REQUEST_TIMEOUT = 30
REQUEST_HEADERS = {"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"}

# Application settings
APP_TITLE = "NSE Tradable Stocks Database Interface"
CONSOLE_WIDTH = 60

# Date format for database storage
DATE_FORMAT = "%Y-%m-%d"

# Pandas to SQLite type mapping
PANDAS_TO_SQLITE_TYPES = {
    'int64': 'INTEGER',
    'int32': 'INTEGER',
    'int16': 'INTEGER',
    'int8': 'INTEGER',
    'float64': 'REAL',
    'float32': 'REAL',
    'object': 'TEXT',
    'bool': 'INTEGER',
    'datetime64[ns]': 'DATE',
    'category': 'TEXT'
}

```

# db-sql\data_processor.py

```py
"""
Unified data processing module for NSE stocks data.
This module consolidates all data loading, cleaning, and processing logic.
"""

import pandas as pd
import requests
import io
from pathlib import Path
from typing import Optional, List, Tuple

from utils import normalize_column_name, ensure_file_exists, print_step
from config import (
    PRIMARY_CSV_URL, BHAV_CSV_URL, LOCAL_CSV_FILES, 
    REQUEST_TIMEOUT, REQUEST_HEADERS, DATE_FORMAT
)


class DataProcessor:
    """Handles all data loading, cleaning, and processing operations."""
    
    def __init__(self, verbose: bool = True):
        """
        Initialize the data processor.
        
        Args:
            verbose (bool): Whether to print detailed processing information
        """
        self.verbose = verbose
    
    def _log(self, message: str):
        """Print message if verbose mode is enabled."""
        if self.verbose:
            print(message)
    
    def load_csv_from_url(self, url: str) -> Optional[pd.DataFrame]:
        """
        Download and load CSV data from a URL.
        
        Args:
            url (str): The URL to download from
            
        Returns:
            Optional[pd.DataFrame]: The loaded DataFrame or None if failed
        """
        try:
            self._log(f"Fetching data from {url}...")
            response = requests.get(url, timeout=REQUEST_TIMEOUT, headers=REQUEST_HEADERS)
            
            if response.status_code == 200:
                df = pd.read_csv(io.BytesIO(response.content))
                self._log(f"Successfully downloaded data: {df.shape[0]} rows, {df.shape[1]} columns")
                return df
            else:
                self._log(f"Failed to download: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            self._log(f"Error downloading from {url}: {e}")
            return None
    
    def load_csv_from_file(self, file_path: str) -> Optional[pd.DataFrame]:
        """
        Load CSV data from a local file.
        
        Args:
            file_path (str): Path to the CSV file
            
        Returns:
            Optional[pd.DataFrame]: The loaded DataFrame or None if failed
        """
        try:
            path = Path(file_path)
            if not path.exists():
                return None
                
            self._log(f"Loading CSV file: {file_path}")
            df = pd.read_csv(file_path)
            self._log(f"Successfully loaded: {df.shape[0]} rows, {df.shape[1]} columns")
            return df
            
        except Exception as e:
            self._log(f"Error loading {file_path}: {e}")
            return None
    
    def load_data_with_fallback(self) -> Tuple[Optional[pd.DataFrame], str]:
        """
        Load data with fallback strategy: try local files first, then URLs.
        
        Returns:
            Tuple[Optional[pd.DataFrame], str]: DataFrame and source description
        """
        # Try local files first
        for csv_file in LOCAL_CSV_FILES:
            df = self.load_csv_from_file(csv_file)
            if df is not None:
                return df, f"local file: {csv_file}"
        
        # Try primary URL
        df = self.load_csv_from_url(PRIMARY_CSV_URL)
        if df is not None:
            return df, f"URL: {PRIMARY_CSV_URL}"
        
        # Try alternative URL
        df = self.load_csv_from_url(BHAV_CSV_URL)
        if df is not None:
            return df, f"URL: {BHAV_CSV_URL}"
        
        return None, "No data source available"
    
    def clean_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Clean and normalize a DataFrame.
        
        Args:
            df (pd.DataFrame): The DataFrame to clean
            
        Returns:
            pd.DataFrame: The cleaned DataFrame
        """
        # Create a copy to avoid modifying the original
        cleaned_df = df.copy()
        
        # Store original columns for comparison
        original_columns = cleaned_df.columns.tolist()
        
        # Normalize column names
        cleaned_df.columns = [normalize_column_name(col) for col in cleaned_df.columns]
        
        # Log column transformations
        if self.verbose:
            self._log("\nColumn name transformations:")
            for orig, new in zip(original_columns, cleaned_df.columns):
                if orig != new:
                    self._log(f"  '{orig}' -> '{new}'")
        
        # Handle date columns
        self._process_date_columns(cleaned_df)
        
        return cleaned_df
    
    def _process_date_columns(self, df: pd.DataFrame):
        """
        Process date columns in the DataFrame.
        
        Args:
            df (pd.DataFrame): DataFrame to process (modified in place)
        """
        # Find potential date columns
        date_columns = [col for col in df.columns if 'date' in col.lower()]
        
        for date_col in date_columns:
            try:
                self._log(f"Processing date column: {date_col}")
                # Convert to datetime
                df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
                self._log(f"  Converted to datetime: {df[date_col].dtype}")
                
                # Count null values after conversion
                null_count = df[date_col].isnull().sum()
                if null_count > 0:
                    self._log(f"  Warning: {null_count} invalid dates converted to NaT")
                    
            except Exception as e:
                self._log(f"  Error processing {date_col}: {e}")
    
    def get_data_summary(self, df: pd.DataFrame) -> dict:
        """
        Get a summary of the DataFrame.
        
        Args:
            df (pd.DataFrame): The DataFrame to summarize
            
        Returns:
            dict: Summary information
        """
        return {
            'shape': df.shape,
            'columns': list(df.columns),
            'dtypes': df.dtypes.to_dict(),
            'null_counts': df.isnull().sum().to_dict(),
            'memory_usage': df.memory_usage(deep=True).sum()
        }
    
    def print_data_summary(self, df: pd.DataFrame, title: str = "Data Summary"):
        """
        Print a formatted summary of the DataFrame.
        
        Args:
            df (pd.DataFrame): The DataFrame to summarize
            title (str): Title for the summary
        """
        if not self.verbose:
            return
            
        print(f"\n{title}")
        print("-" * len(title))
        print(f"Shape: {df.shape[0]} rows × {df.shape[1]} columns")
        print(f"Columns: {list(df.columns)}")
        print(f"\nData types:")
        for col, dtype in df.dtypes.items():
            print(f"  {col}: {dtype}")
        
        # Show null counts if any
        null_counts = df.isnull().sum()
        if null_counts.sum() > 0:
            print(f"\nNull values:")
            for col, count in null_counts.items():
                if count > 0:
                    print(f"  {col}: {count}")
        
        print(f"\nSample data:")
        print(df.head(3).to_string())

```

# db-sql\database_manager.py

```py
"""
Unified database management module for NSE stocks data.
This module handles all database operations including schema creation, connection management, and data population.
"""

import sqlite3
import pandas as pd
from pathlib import Path
from typing import Optional, List, Tuple, Dict, Any
from contextlib import contextmanager

from utils import print_step
from config import DB_FILE, TABLE_NAME, PANDAS_TO_SQLITE_TYPES, DATE_FORMAT


class DatabaseManager:
    """Handles all database operations with proper schema management."""
    
    def __init__(self, db_file: str = DB_FILE, verbose: bool = True):
        """
        Initialize the database manager.
        
        Args:
            db_file (str): Path to the SQLite database file
            verbose (bool): Whether to print detailed information
        """
        self.db_file = db_file
        self.verbose = verbose
        self.table_name = TABLE_NAME
    
    def _log(self, message: str):
        """Print message if verbose mode is enabled."""
        if self.verbose:
            print(message)
    
    @contextmanager
    def get_connection(self):
        """
        Context manager for database connections.
        
        Yields:
            sqlite3.Connection: Database connection
        """
        conn = None
        try:
            conn = sqlite3.connect(self.db_file)
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            raise e
        finally:
            if conn:
                conn.close()
    
    def map_pandas_dtype_to_sqlite(self, dtype) -> str:
        """
        Map pandas dtype to appropriate SQLite type.
        
        Args:
            dtype: Pandas dtype
            
        Returns:
            str: SQLite type string
        """
        dtype_str = str(dtype)
        
        # Handle datetime types
        if 'datetime' in dtype_str:
            return 'DATE'
        
        # Use mapping from config
        return PANDAS_TO_SQLITE_TYPES.get(dtype_str, 'TEXT')
    
    def generate_create_table_sql(self, df: pd.DataFrame) -> str:
        """
        Generate CREATE TABLE SQL statement from DataFrame schema.
        
        Args:
            df (pd.DataFrame): DataFrame to analyze
            
        Returns:
            str: CREATE TABLE SQL statement
        """
        columns_sql = []
        
        if self.verbose:
            self._log("Mapping pandas dtypes to SQLite types:")
            self._log("-" * 50)
        
        for column, dtype in df.dtypes.items():
            sqlite_type = self.map_pandas_dtype_to_sqlite(dtype)
            columns_sql.append(f"    {column} {sqlite_type}")
            
            if self.verbose:
                self._log(f"{column:20} | {str(dtype):15} -> {sqlite_type}")
        
        # Join all column definitions
        columns_definition = ",\n".join(columns_sql)
        
        # Build the complete CREATE TABLE statement
        create_table_sql = f"""CREATE TABLE IF NOT EXISTS {self.table_name} (
{columns_definition}
);"""
        
        return create_table_sql
    
    def create_table_from_dataframe(self, df: pd.DataFrame) -> bool:
        """
        Create database table based on DataFrame schema.
        
        Args:
            df (pd.DataFrame): DataFrame to base schema on
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            create_sql = self.generate_create_table_sql(df)
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Drop existing table
                cursor.execute(f"DROP TABLE IF EXISTS {self.table_name}")
                self._log(f"Dropped existing table: {self.table_name}")
                
                # Create new table
                cursor.execute(create_sql)
                self._log(f"Created table: {self.table_name}")
                
                if self.verbose:
                    self._log("\nGenerated SQL:")
                    self._log(create_sql)
                
                conn.commit()
                return True
                
        except Exception as e:
            self._log(f"Error creating table: {e}")
            return False
    
    def populate_table(self, df: pd.DataFrame) -> bool:
        """
        Populate the database table with DataFrame data.
        
        Args:
            df (pd.DataFrame): DataFrame containing data to insert
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Prepare DataFrame for database insertion
            df_to_insert = df.copy()
            
            # Convert datetime columns to string format for SQLite
            for col in df_to_insert.columns:
                if df_to_insert[col].dtype == 'datetime64[ns]':
                    df_to_insert[col] = df_to_insert[col].dt.strftime(DATE_FORMAT)
                    self._log(f"Converted {col} to date string format")
            
            with self.get_connection() as conn:
                # Use pandas to_sql for efficient insertion
                df_to_insert.to_sql(
                    self.table_name, 
                    conn, 
                    if_exists='replace', 
                    index=False
                )
                
                # Verify insertion
                cursor = conn.cursor()
                cursor.execute(f"SELECT COUNT(*) FROM {self.table_name}")
                count = cursor.fetchone()[0]
                
                self._log(f"Successfully inserted {count} records into {self.table_name}")
                
                # Show sample data
                if self.verbose:
                    self._show_sample_data(cursor)
                
                conn.commit()
                return True
                
        except Exception as e:
            self._log(f"Error populating table: {e}")
            return False
    
    def _show_sample_data(self, cursor: sqlite3.Cursor, limit: int = 3):
        """Show sample data from the table."""
        try:
            cursor.execute(f"SELECT * FROM {self.table_name} LIMIT {limit}")
            sample_data = cursor.fetchall()
            
            if sample_data:
                # Get column names
                cursor.execute(f"PRAGMA table_info({self.table_name})")
                columns = [col[1] for col in cursor.fetchall()]
                
                self._log(f"\nSample data from {self.table_name}:")
                self._log("-" * 60)
                
                for i, row in enumerate(sample_data, 1):
                    self._log(f"Record {i}:")
                    for col_name, value in zip(columns, row):
                        self._log(f"  {col_name}: {value}")
                    self._log("-" * 30)
                    
        except Exception as e:
            self._log(f"Error showing sample data: {e}")
    
    def get_table_info(self) -> Optional[List[Tuple]]:
        """
        Get table schema information.
        
        Returns:
            Optional[List[Tuple]]: Table info or None if error
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"PRAGMA table_info({self.table_name})")
                return cursor.fetchall()
        except Exception as e:
            self._log(f"Error getting table info: {e}")
            return None
    
    def execute_query(self, query: str, params: Optional[Tuple] = None) -> Optional[Tuple[List[str], List[Tuple]]]:
        """
        Execute a SQL query and return results with headers.
        
        Args:
            query (str): SQL query to execute
            params (Optional[Tuple]): Query parameters
            
        Returns:
            Optional[Tuple[List[str], List[Tuple]]]: (headers, results) or None if error
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if params:
                    cursor.execute(query, params)
                else:
                    cursor.execute(query)
                
                results = cursor.fetchall()
                headers = [desc[0] for desc in cursor.description] if cursor.description else []
                
                return headers, results
                
        except Exception as e:
            self._log(f"Error executing query: {e}")
            return None
    
    def table_exists(self) -> bool:
        """
        Check if the main table exists.
        
        Returns:
            bool: True if table exists, False otherwise
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (self.table_name,))
                return cursor.fetchone() is not None
        except Exception:
            return False
    
    def get_record_count(self) -> int:
        """
        Get the number of records in the table.
        
        Returns:
            int: Number of records, -1 if error
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT COUNT(*) FROM {self.table_name}")
                return cursor.fetchone()[0]
        except Exception:
            return -1

```

# db-sql\db_interface_enhanced.py

```py
import sqlite3
from tabulate import tabulate
from datetime import datetime

from utils import clear_screen, print_section_header
from config import APP_TITLE, CONSOLE_WIDTH, DB_FILE, TABLE_NAME
from database_manager import DatabaseManager

def print_header():
    """Print the application header"""
    print_section_header("TRADABLE STOCKS DATABASE QUERY INTERFACE", CONSOLE_WIDTH)

def print_menu():
    """Display the main menu"""
    print("\n--- MENU OPTIONS ---")
    print("1. Show all stocks")
    print("2. Search stocks by symbol")
    print("3. Search stocks by industry")
    print("4. Show database schema")
    print("5. Execute custom SQL query")
    print("6. Count total stocks")
    print("7. Exit")
    print("-" * 20)

def execute_query(conn, query, params=None):
    """Execute a query and return results with column headers"""
    cursor = conn.cursor()
    try:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        results = cursor.fetchall()
        if results:
            headers = [desc[0] for desc in cursor.description]
            return headers, results
        else:
            return None, []
    except sqlite3.Error as e:
        print(f"\nError executing query: {e}")
        return None, None

def show_all_stocks(db_manager):
    """Display all stocks in the database"""
    query = f"SELECT * FROM {TABLE_NAME}"
    result = db_manager.execute_query(query)

    if result and result[1]:
        headers, results = result
        print("\n=== ALL STOCKS ===")
        print(tabulate(results, headers=headers, tablefmt="grid"))
        print(f"\nTotal records: {len(results)}")
    else:
        print("\nNo stocks found in the database.")

def search_by_symbol(db_manager):
    """Search stocks by symbol"""
    symbol = input("\nEnter stock symbol (or partial symbol): ").strip().upper()

    # Try different possible column names for symbol
    possible_columns = ['symbol', 'stock_symbol', 'company_symbol', 'ticker']

    for col in possible_columns:
        query = f"SELECT * FROM {TABLE_NAME} WHERE {col} LIKE ?"
        result = db_manager.execute_query(query, (f"%{symbol}%",))

        if result and result[1]:
            headers, results = result
            print(f"\n=== STOCKS MATCHING '{symbol}' ===")
            print(tabulate(results, headers=headers, tablefmt="grid"))
            print(f"\nFound {len(results)} matching record(s)")
            return

    print(f"\nNo stocks found matching '{symbol}' or symbol column not found")

def search_by_industry(db_manager):
    """Search stocks by industry"""
    industry = input("\nEnter industry name (or partial name): ").strip()

    # Try different possible column names for industry
    possible_columns = ['industry', 'sector', 'business_segment']

    for col in possible_columns:
        query = f"SELECT * FROM {TABLE_NAME} WHERE {col} LIKE ?"
        result = db_manager.execute_query(query, (f"%{industry}%",))

        if result and result[1]:
            headers, results = result
            print(f"\n=== STOCKS IN '{industry}' INDUSTRY ===")
            print(tabulate(results, headers=headers, tablefmt="grid"))
            print(f"\nFound {len(results)} matching record(s)")
            return

    print(f"\nNo stocks found in '{industry}' industry or industry column not found")

def show_schema(db_manager):
    """Display the database schema"""
    schema_info = db_manager.get_table_info()

    if schema_info:
        print("\n=== DATABASE SCHEMA ===")
        print(f"\nTable: {TABLE_NAME}")
        print("-" * 50)

        headers = ["Column ID", "Name", "Type", "Not Null", "Default", "Primary Key"]
        print(tabulate(schema_info, headers=headers, tablefmt="grid"))
        print(f"\nTotal columns: {len(schema_info)}")
    else:
        print("\nError retrieving schema information.")

def execute_custom_query(db_manager):
    """Execute a custom SQL query"""
    print("\n=== CUSTOM SQL QUERY ===")
    print("Enter your SQL query (type 'cancel' to return to menu):")
    print(f"Example: SELECT * FROM {TABLE_NAME} LIMIT 10")
    query = input("> ").strip()

    if query.lower() == 'cancel':
        return

    if not query:
        print("Empty query entered.")
        return

    result = db_manager.execute_query(query)

    if result:
        headers, results = result
        if results:
            print("\nQuery Results:")
            print(tabulate(results, headers=headers, tablefmt="grid"))
            print(f"\nReturned {len(results)} row(s)")
        else:
            print("\nQuery executed successfully with no results.")
    else:
        print("\nError executing query.")

def count_stocks(db_manager):
    """Count total stocks in the database"""
    count = db_manager.get_record_count()

    if count >= 0:
        print(f"\n=== TOTAL STOCKS IN DATABASE: {count} ===")

        # Try to show count by industry if industry column exists
        possible_industry_cols = ['industry', 'sector', 'business_segment']

        for col in possible_industry_cols:
            query = f"""
                SELECT {col}, COUNT(*) as count
                FROM {TABLE_NAME}
                GROUP BY {col}
                ORDER BY count DESC
            """
            result = db_manager.execute_query(query)

            if result and result[1]:
                headers, results = result
                print(f"\n=== STOCKS BY {col.upper()} ===")
                print(tabulate(results, headers=headers, tablefmt="grid"))
                break
    else:
        print("\nError counting stocks.")

def main():
    """Main application loop"""
    try:
        # Initialize database manager
        db_manager = DatabaseManager(DB_FILE, verbose=False)

        # Check if database exists and has data
        if not db_manager.table_exists():
            print(f"Error: Database table '{TABLE_NAME}' not found!")
            print("Please run the main application to setup the database first.")
            input("\nPress Enter to exit...")
            return

        record_count = db_manager.get_record_count()
        if record_count <= 0:
            print("Database table exists but contains no data.")
            print("Please run the main application to populate the database.")
            input("\nPress Enter to exit...")
            return

        while True:
            clear_screen()
            print_header()
            print_menu()

            choice = input("\nEnter your choice (1-7): ").strip()

            if choice == '1':
                show_all_stocks(db_manager)
            elif choice == '2':
                search_by_symbol(db_manager)
            elif choice == '3':
                search_by_industry(db_manager)
            elif choice == '4':
                show_schema(db_manager)
            elif choice == '5':
                execute_custom_query(db_manager)
            elif choice == '6':
                count_stocks(db_manager)
            elif choice == '7':
                print("\nThank you for using the Tradable Stocks Database Interface!")
                print("Goodbye!")
                break
            else:
                print("\nInvalid choice! Please select a number between 1 and 7.")

            if choice != '7':
                input("\nPress Enter to continue...")

    except Exception as e:
        print(f"\nFatal error: {e}")
        input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()

```

# db-sql\download_nse_csv.py

```py
"""
NSE data download module - now uses the unified data processing and database management modules.
This module maintains backward compatibility while using the new modular architecture.
"""

from data_processor import DataProcessor
from database_manager import DatabaseManager
from config import BHAV_CSV_URL
from utils import print_section_header


def download_and_update_db():
    """
    Download NSE data and update the database using the new modular architecture.
    """
    print_section_header("Download and Update NSE Data")

    # Initialize processors
    data_processor = DataProcessor(verbose=True)
    db_manager = DatabaseManager(verbose=True)

    try:
        print(f"Fetching data from {BHAV_CSV_URL}...")

        # Download data using the data processor
        df = data_processor.load_csv_from_url(BHAV_CSV_URL)

        if df is None:
            print("✗ Failed to download data")
            return False

        print("✓ Data fetched successfully.")

        # Clean the data
        print("Cleaning data...")
        cleaned_df = data_processor.clean_dataframe(df)

        # Show data summary
        data_processor.print_data_summary(cleaned_df, "Downloaded Data Summary")

        # Create/update database table
        print("Creating database table...")
        if not db_manager.create_table_from_dataframe(cleaned_df):
            print("✗ Failed to create database table")
            return False

        # Populate database
        print("Updating database...")
        if not db_manager.populate_table(cleaned_df):
            print("✗ Failed to update database")
            return False

        print("✓ Database updated with new data.")
        return True

    except Exception as e:
        print(f"✗ An error occurred: {e}")
        return False


def main():
    """Main entry point."""
    success = download_and_update_db()
    if not success:
        print("\n✗ Failed to download and update database")
        input("Press Enter to exit...")
    else:
        print("\n✓ Successfully downloaded and updated database")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()

```

# db-sql\generate_sql_schema.py

```py
"""
SQL schema generation module - now uses the unified database manager.
This module maintains backward compatibility while using the new modular architecture.
"""

from data_processor import DataProcessor
from database_manager import DatabaseManager
from utils import print_section_header

def map_pandas_dtype_to_sqlite(dtype):
    """
    Map pandas dtypes to SQLite types
    """
    dtype_str = str(dtype).lower()
    
    # Integer types
    if 'int' in dtype_str:
        return 'INTEGER'
    
    # Float types
    elif 'float' in dtype_str:
        return 'REAL'
    
    # Boolean types
    elif 'bool' in dtype_str:
        return 'INTEGER'  # SQLite stores booleans as integers
    
    # Datetime types
    elif 'datetime' in dtype_str or 'timestamp' in dtype_str:
        return 'TEXT'  # Store as ISO format text
    
    # Object/string types and everything else
    else:
        return 'TEXT'

def generate_create_table_sql(df, table_name):
    """
    Generate CREATE TABLE SQL statement dynamically from DataFrame columns and dtypes
    """
    columns_sql = []
    
    print(f"Mapping pandas dtypes to SQLite types:")
    print("-" * 50)
    
    for column, dtype in df.dtypes.items():
        sqlite_type = map_pandas_dtype_to_sqlite(dtype)
        columns_sql.append(f"    {column} {sqlite_type}")
        print(f"{column:20} | {str(dtype):15} -> {sqlite_type}")
    
    # Join all column definitions
    columns_definition = ",\n".join(columns_sql)
    
    # Build the complete CREATE TABLE statement
    create_table_sql = f"""CREATE TABLE {table_name} (
{columns_definition}
);"""
    
    return create_table_sql

def create_database_table(df, table_name, db_path="tradable_stocks.db"):
    """
    Create SQLite database and execute CREATE TABLE statement
    """
    # Generate the CREATE TABLE SQL
    create_sql = generate_create_table_sql(df, table_name)
    
    print(f"\nGenerated CREATE TABLE statement:")
    print("=" * 60)
    print(create_sql)
    print("=" * 60)
    
    # Connect to SQLite database (creates file if it doesn't exist)
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Drop table if it exists (for clean slate)
        cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
        print(f"\nDropped existing '{table_name}' table if it existed.")
        
        # Execute CREATE TABLE statement
        cursor.execute(create_sql)
        print(f"Successfully created '{table_name}' table in '{db_path}'")
        
        # Commit changes
        conn.commit()
        
        # Verify table creation
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
        result = cursor.fetchone()
        
        if result:
            print(f"✓ Table '{table_name}' verified in database")
            
            # Get table schema info
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns_info = cursor.fetchall()
            
            print(f"\nTable schema verification:")
            print("-" * 50)
            for col_info in columns_info:
                col_id, col_name, col_type, not_null, default_val, pk = col_info
                print(f"{col_name:20} | {col_type}")
        else:
            print(f"✗ Failed to verify table '{table_name}' creation")
            
    except sqlite3.Error as e:
        print(f"SQLite error: {e}")
        return False
    except Exception as e:
        print(f"Error: {e}")
        return False
    finally:
        if conn:
            conn.close()
            print(f"\nDatabase connection closed.")
    
    return True

def main():
    """
    Main function to load CSV data and create SQL schema
    """
    print("Step 6: Generate a matching SQL schema")
    print("=" * 50)
    
    # Try to load data from the sample CSV first
    csv_files = ["sample_with_dates.csv", r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"]
    
    df = None
    csv_used = None
    
    for csv_path in csv_files:
        if Path(csv_path).exists():
            print(f"Loading CSV file: {csv_path}")
            try:
                # Load the CSV into DataFrame
                df = pd.read_csv(csv_path)
                
                # Strip and normalize column names to snake_case
                original_columns = df.columns.tolist()
                df.columns = [normalize_column_name(col) for col in df.columns]
                
                print(f"Column name transformations:")
                for orig, new in zip(original_columns, df.columns):
                    if orig != new:
                        print(f"  '{orig}' -> '{new}'")
                
                # Convert date columns to datetime if they exist
                date_columns = [col for col in df.columns if 'date_of_listing' in col.lower()]
                if date_columns:
                    for date_col in date_columns:
                        print(f"Converting '{date_col}' to datetime64[ns]...")
                        df[date_col] = pd.to_datetime(df[date_col])
                        print(f"  Data type after conversion: {df[date_col].dtype}")
                
                csv_used = csv_path
                break
                
            except Exception as e:
                print(f"Error loading {csv_path}: {e}")
                continue
    
    if df is None:
        print("No valid CSV file found. Creating a sample DataFrame for demonstration...")
        # Create a sample DataFrame that matches the expected structure
        df = pd.DataFrame({
            'symbol': ['RELIANCE', 'TCS', 'HDFCBANK'],
            'company_name': ['Reliance Industries Ltd.', 'Tata Consultancy Services Ltd.', 'HDFC Bank Ltd.'],
            'industry': ['Oil Gas & Consumable Fuels', 'Information Technology', 'Financial Services'],
            'date_of_listing': pd.to_datetime(['1995-11-29', '2004-08-25', '1995-11-08'])
        })
    
    print(f"\nDataFrame shape: {df.shape}")
    print(f"DataFrame columns: {list(df.columns)}")
    print(f"\nDataFrame dtypes:")
    print(df.dtypes)
    print(f"\nDataFrame head:")
    print(df.head())
    
    # Generate the CREATE TABLE SQL using the new database manager
    db_manager = DatabaseManager(verbose=True)
    create_table_sql = db_manager.generate_create_table_sql(df)

    print(f"\n" + "="*60)
    print("GENERATED SQL SCHEMA:")
    print("="*60)
    print(create_table_sql)

    # Optionally create the database and table
    create_database = input("\nDo you want to create the database table? (y/n): ").lower().strip()
    if create_database == 'y':
        if db_manager.create_table_from_dataframe(df):
            print("✓ Database table created successfully!")
        else:
            print("✗ Failed to create database table")

    print(f"\n" + "="*60)
    print("Schema generation completed!")
    print("="*60)

if __name__ == "__main__":
    main()

```

# db-sql\load_and_clean_data.py

```py
"""
Data loading and cleaning module - now a wrapper around the unified data processor.
This module maintains backward compatibility while using the new modular architecture.
"""

from data_processor import DataProcessor
from utils import ensure_file_exists

def load_and_clean_data(csv_path):
    """
    Load CSV data into DataFrame and clean it according to specifications.
    This function now uses the unified DataProcessor for consistency.

    Args:
        csv_path (str): Path to the CSV file to load

    Returns:
        pd.DataFrame: Cleaned DataFrame

    Raises:
        FileNotFoundError: If the CSV file doesn't exist
    """
    # Ensure file exists
    ensure_file_exists(csv_path, "CSV file")

    # Initialize data processor
    data_processor = DataProcessor(verbose=True)

    # Load the CSV file
    df = data_processor.load_csv_from_file(csv_path)

    if df is None:
        raise RuntimeError(f"Failed to load CSV file: {csv_path}")

    # Clean the DataFrame
    cleaned_df = data_processor.clean_dataframe(df)

    # Print summary information
    data_processor.print_data_summary(cleaned_df, f"Loaded and Cleaned Data from {csv_path}")

    return cleaned_df

if __name__ == "__main__":
    # Path to the CSV file
    csv_file_path = r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"
    
    try:
        # Load and clean the data
        df = load_and_clean_data(csv_file_path)
        
        # Additional info about the loaded data
        print(f"\nSummary:")
        print(f"Successfully loaded {df.shape[0]} rows and {df.shape[1]} columns")
        
        # If there are any missing values, report them
        missing_data = df.isnull().sum()
        if missing_data.any():
            print(f"\nMissing values per column:")
            print(missing_data[missing_data > 0])
        else:
            print(f"\nNo missing values found in the dataset")
            
    except Exception as e:
        print(f"Error loading data: {e}")

```

# db-sql\main.py

```py
#!/usr/bin/env python3
"""
Main entry point for the NSE Tradable Stocks Database Application.
This provides a guided workflow for the entire application.
"""

import sys
import subprocess
from pathlib import Path

from utils import clear_screen, print_section_header, print_step
from config import APP_TITLE, CONSOLE_WIDTH
from data_processor import DataProcessor
from database_manager import DatabaseManager


class NSEStocksApp:
    """Main application class that orchestrates the entire workflow."""
    
    def __init__(self):
        """Initialize the application."""
        self.data_processor = DataProcessor(verbose=True)
        self.db_manager = DatabaseManager(verbose=True)
    
    def check_dependencies(self) -> bool:
        """
        Check if required dependencies are installed.
        
        Returns:
            bool: True if all dependencies are available
        """
        print_step(1, "Checking Dependencies")
        
        required_modules = [
            'pandas', 'sqlite3', 'requests', 'tabulate'
        ]
        
        missing_modules = []
        
        for module in required_modules:
            try:
                __import__(module)
                print(f"✓ {module}")
            except ImportError:
                print(f"✗ {module} (missing)")
                missing_modules.append(module)
        
        if missing_modules:
            print(f"\nMissing dependencies: {', '.join(missing_modules)}")
            print("Please install them using: pip install -r requirements.txt")
            return False
        
        print("\n✓ All dependencies are available!")
        return True
    
    def prompt_data_refresh(self) -> bool:
        """
        Ask user if they want to download fresh data.
        
        Returns:
            bool: True if user wants to refresh data
        """
        print_step(2, "Data Source Selection")
        
        # Check if database already exists and has data
        if self.db_manager.table_exists():
            record_count = self.db_manager.get_record_count()
            if record_count > 0:
                print(f"Existing database found with {record_count} records.")
        
        print("\nData source options:")
        print("1. Use existing data (if available)")
        print("2. Download fresh data from NSE")
        print("3. Load from local CSV files")
        
        while True:
            choice = input("\nSelect option (1-3): ").strip()
            if choice == '1':
                return False  # Don't refresh
            elif choice == '2':
                return True   # Download fresh
            elif choice == '3':
                return False  # Use local files
            else:
                print("Please enter 1, 2, or 3")
    
    def setup_database(self, refresh_data: bool = False) -> bool:
        """
        Set up the database with data.
        
        Args:
            refresh_data (bool): Whether to download fresh data
            
        Returns:
            bool: True if setup successful
        """
        print_step(3, "Database Setup")
        
        # Check if we can use existing data
        if not refresh_data and self.db_manager.table_exists():
            record_count = self.db_manager.get_record_count()
            if record_count > 0:
                print(f"Using existing database with {record_count} records.")
                return True
        
        # Load data
        print("Loading data...")
        df, source = self.data_processor.load_data_with_fallback()
        
        if df is None:
            print("✗ Failed to load data from any source")
            return False
        
        print(f"✓ Data loaded from {source}")
        
        # Clean the data
        print("Cleaning data...")
        cleaned_df = self.data_processor.clean_dataframe(df)
        
        # Show data summary
        self.data_processor.print_data_summary(cleaned_df, "Cleaned Data Summary")
        
        # Create database table
        print("\nCreating database table...")
        if not self.db_manager.create_table_from_dataframe(cleaned_df):
            print("✗ Failed to create database table")
            return False
        
        # Populate database
        print("Populating database...")
        if not self.db_manager.populate_table(cleaned_df):
            print("✗ Failed to populate database")
            return False
        
        print("✓ Database setup completed successfully!")
        return True
    
    def launch_interface(self):
        """Launch the database query interface."""
        print_step(4, "Launching Interface")
        
        try:
            # Import and run the enhanced interface
            from db_interface_enhanced import main as run_interface
            print("Starting the database query interface...")
            print("(Press Ctrl+C to return to this menu)\n")
            run_interface()
            
        except KeyboardInterrupt:
            print("\n\nReturned to main menu.")
        except ImportError as e:
            print(f"✗ Error importing interface: {e}")
        except Exception as e:
            print(f"✗ Error launching interface: {e}")
    
    def show_main_menu(self):
        """Display the main application menu."""
        while True:
            clear_screen()
            print_section_header(APP_TITLE, CONSOLE_WIDTH)
            
            print("Main Menu:")
            print("1. Setup/Refresh Database")
            print("2. Launch Query Interface")
            print("3. Check System Status")
            print("4. Exit")
            print("-" * 30)
            
            choice = input("Select option (1-4): ").strip()
            
            if choice == '1':
                refresh = self.prompt_data_refresh()
                if self.setup_database(refresh):
                    input("\nPress Enter to continue...")
                else:
                    input("\nSetup failed. Press Enter to continue...")
            
            elif choice == '2':
                if not self.db_manager.table_exists():
                    print("Database not found. Please setup database first.")
                    input("Press Enter to continue...")
                else:
                    self.launch_interface()
            
            elif choice == '3':
                self.show_system_status()
                input("\nPress Enter to continue...")
            
            elif choice == '4':
                print("Goodbye!")
                break
            
            else:
                print("Please enter 1, 2, 3, or 4")
                input("Press Enter to continue...")
    
    def show_system_status(self):
        """Display current system status."""
        print_section_header("System Status", CONSOLE_WIDTH)
        
        # Check dependencies
        print("Dependencies:")
        self.check_dependencies()
        
        # Check database
        print(f"\nDatabase Status:")
        if self.db_manager.table_exists():
            record_count = self.db_manager.get_record_count()
            print(f"✓ Database exists with {record_count} records")
            
            # Show table info
            table_info = self.db_manager.get_table_info()
            if table_info:
                print(f"✓ Table schema: {len(table_info)} columns")
                for col_info in table_info:
                    print(f"  - {col_info[1]} ({col_info[2]})")
        else:
            print("✗ Database not found")
        
        # Check files
        print(f"\nFile Status:")
        db_path = Path(self.db_manager.db_file)
        print(f"Database file: {db_path.absolute()}")
        print(f"  Exists: {'✓' if db_path.exists() else '✗'}")
        if db_path.exists():
            size_mb = db_path.stat().st_size / (1024 * 1024)
            print(f"  Size: {size_mb:.2f} MB")
    
    def run(self):
        """Run the main application."""
        try:
            # Initial setup check
            if not self.check_dependencies():
                input("\nPress Enter to exit...")
                return
            
            # Show main menu
            self.show_main_menu()
            
        except KeyboardInterrupt:
            print("\n\nApplication interrupted by user.")
        except Exception as e:
            print(f"\nUnexpected error: {e}")
            input("Press Enter to exit...")


def main():
    """Main entry point."""
    app = NSEStocksApp()
    app.run()


if __name__ == "__main__":
    main()

```

# db-sql\populate_database.py

```py
"""
Database population script using the unified data processing and database management modules.
This script is now a thin wrapper around the new modular architecture.
"""

from data_processor import DataProcessor
from database_manager import DatabaseManager
from utils import print_section_header

def populate_database():
    """
    Load CSV data and populate the database using the new modular architecture.
    """
    print_section_header("Populate Database with Cleaned Data")

    # Initialize processors
    data_processor = DataProcessor(verbose=True)
    db_manager = DatabaseManager(verbose=True)

    # Load data with fallback strategy
    print("Loading data...")
    df, source = data_processor.load_data_with_fallback()

    if df is None:
        print("✗ Error: No valid data source found!")
        return False

    print(f"✓ Successfully loaded data from {source}")

    # Clean the data
    print("\nCleaning data...")
    cleaned_df = data_processor.clean_dataframe(df)

    # Show data summary
    data_processor.print_data_summary(cleaned_df, "Cleaned Data Summary")

    # Create database table with proper schema
    print("\nCreating database table...")
    if not db_manager.create_table_from_dataframe(cleaned_df):
        print("✗ Failed to create database table")
        return False

    # Populate database
    print("\nPopulating database...")
    if not db_manager.populate_table(cleaned_df):
        print("✗ Failed to populate database")
        return False

    print("\n✓ Database population completed successfully!")
    return True

if __name__ == "__main__":
    success = populate_database()
    if not success:
        print("\n✗ Failed to populate database")
    input("\nPress Enter to exit...")

```

# db-sql\requirements.txt

```txt
tabulate==0.9.0
pandas==2.1.4
requests==2.31.0

```

# db-sql\run_nse_stocks_app.bat

```bat
@echo off
title NSE Tradable Stocks Database Application
echo.
echo ========================================
echo   NSE Tradable Stocks Database App
echo ========================================
echo.
echo Starting the unified NSE stocks application...
echo This will guide you through the complete workflow.
echo.
uv run python main.py
echo.
echo Application closed.
pause

```

# db-sql\test_modules.py

```py
#!/usr/bin/env python3
"""
Test script to verify all modules import and work correctly.
"""

def test_imports():
    """Test that all modules can be imported."""
    print("Testing module imports...")
    
    try:
        from utils import normalize_column_name, clear_screen, print_section_header
        print("✓ utils module imported successfully")
    except Exception as e:
        print(f"✗ utils module import failed: {e}")
        return False
    
    try:
        from config import DB_FILE, TABLE_NAME, PRIMARY_CSV_URL
        print("✓ config module imported successfully")
    except Exception as e:
        print(f"✗ config module import failed: {e}")
        return False
    
    try:
        from data_processor import DataProcessor
        print("✓ data_processor module imported successfully")
    except Exception as e:
        print(f"✗ data_processor module import failed: {e}")
        return False
    
    try:
        from database_manager import DatabaseManager
        print("✓ database_manager module imported successfully")
    except Exception as e:
        print(f"✗ database_manager module import failed: {e}")
        return False
    
    try:
        from main import NSEStocksApp
        print("✓ main module imported successfully")
    except Exception as e:
        print(f"✗ main module import failed: {e}")
        return False
    
    return True

def test_basic_functionality():
    """Test basic functionality of key modules."""
    print("\nTesting basic functionality...")
    
    # Test utils
    from utils import normalize_column_name
    test_name = normalize_column_name("Company Name & Details")
    expected = "company_name_details"
    if test_name == expected:
        print("✓ normalize_column_name works correctly")
    else:
        print(f"✗ normalize_column_name failed: got '{test_name}', expected '{expected}'")
        return False
    
    # Test data processor initialization
    try:
        from data_processor import DataProcessor
        processor = DataProcessor(verbose=False)
        print("✓ DataProcessor initializes correctly")
    except Exception as e:
        print(f"✗ DataProcessor initialization failed: {e}")
        return False
    
    # Test database manager initialization
    try:
        from database_manager import DatabaseManager
        db_manager = DatabaseManager(verbose=False)
        print("✓ DatabaseManager initializes correctly")
    except Exception as e:
        print(f"✗ DatabaseManager initialization failed: {e}")
        return False
    
    return True

def main():
    """Run all tests."""
    print("=" * 50)
    print("  NSE Stocks Application Module Tests")
    print("=" * 50)
    
    if not test_imports():
        print("\n✗ Import tests failed")
        return False
    
    if not test_basic_functionality():
        print("\n✗ Functionality tests failed")
        return False
    
    print("\n" + "=" * 50)
    print("✓ All tests passed! The refactored application is ready.")
    print("=" * 50)
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        exit(1)

```

# db-sql\tradable_stocks.db

This is a binary file of the type: Binary

# db-sql\utils.py

```py
"""
Shared utility functions for the NSE stocks database application.
This module contains common functions used across multiple modules to eliminate code duplication.
"""

import re
import os
import platform
from pathlib import Path


def normalize_column_name(col_name):
    """
    Convert column name to snake_case format.
    
    This function:
    - Strips whitespace
    - Replaces spaces and special characters with underscores
    - Converts to lowercase
    - Removes multiple consecutive underscores
    - Removes leading/trailing underscores
    
    Args:
        col_name (str): The original column name
        
    Returns:
        str: The normalized column name in snake_case format
    """
    # Strip whitespace
    col_name = col_name.strip()
    # Replace spaces and special characters with underscores
    col_name = re.sub(r'[\s\-\.\&]+', '_', col_name)
    # Convert to lowercase
    col_name = col_name.lower()
    # Remove multiple underscores
    col_name = re.sub(r'_+', '_', col_name)
    # Remove leading/trailing underscores
    col_name = col_name.strip('_')
    return col_name


def clear_screen():
    """Clear the console screen based on the operating system."""
    if platform.system() == "Windows":
        os.system('cls')
    else:
        os.system('clear')


def get_project_root():
    """
    Get the project root directory.
    
    Returns:
        Path: The path to the project root directory
    """
    return Path(__file__).parent


def ensure_file_exists(file_path, description="File"):
    """
    Check if a file exists and raise a descriptive error if it doesn't.
    
    Args:
        file_path (str or Path): Path to the file to check
        description (str): Description of the file for error messages
        
    Raises:
        FileNotFoundError: If the file doesn't exist
    """
    path = Path(file_path)
    if not path.exists():
        raise FileNotFoundError(f"{description} not found: {path.absolute()}")


def print_section_header(title, width=60):
    """
    Print a formatted section header.
    
    Args:
        title (str): The title to display
        width (int): Width of the header line
    """
    print("=" * width)
    print(f"  {title}")
    print("=" * width)
    print()


def print_step(step_number, description):
    """
    Print a formatted step description.
    
    Args:
        step_number (int): The step number
        description (str): Description of the step
    """
    print(f"\nStep {step_number}: {description}")
    print("-" * (len(description) + 10))

```

