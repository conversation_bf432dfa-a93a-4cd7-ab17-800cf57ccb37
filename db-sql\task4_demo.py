import pandas as pd
import re

def normalize_to_snake_case(column_name):
    """Convert column name to snake_case"""
    # Strip whitespace first
    column_name = column_name.strip()
    # Replace spaces and special characters with underscores
    column_name = re.sub(r'[\s\-\.\&]+', '_', column_name)
    # Convert to lowercase
    column_name = column_name.lower()
    # Clean up multiple underscores
    column_name = re.sub(r'_+', '_', column_name)
    # Remove leading/trailing underscores
    column_name = column_name.strip('_')
    return column_name

# Parse the CSV into a DataFrame
csv_path = r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv"
df = pd.read_csv(csv_path)

# Strip column names and normalize to snake_case
df.columns = df.columns.str.strip()  # Strip whitespace from column names
df.columns = [normalize_to_snake_case(col) for col in df.columns]  # Normalize to snake_case

# Convert DATE_OF_LISTING to datetime64[ns] if it exists
# Note: The current CSV doesn't have this column, but this shows how it would be done
if 'date_of_listing' in df.columns:
    df['date_of_listing'] = pd.to_datetime(df['date_of_listing'])
    print(f"Converted date_of_listing to {df['date_of_listing'].dtype}")
else:
    print("DATE_OF_LISTING column not present in this dataset")

# Print df.shape and df.head() to confirm the load went as expected
print(f"df.shape: {df.shape}")
print(f"\ndf.head():")
print(df.head())
