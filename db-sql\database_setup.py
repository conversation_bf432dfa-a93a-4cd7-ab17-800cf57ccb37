import sqlite3
import pathlib
from config import DB_FILE

# Create / connect to the SQLite database
print(f"Connecting to database: {DB_FILE}")

# Open a connection
con = sqlite3.connect(DB_FILE)
cur = con.cursor()

print(f"Successfully connected to {DB_FILE}")

# Optionally remove any existing table
print("Removing existing 'tradable_stocks' table if it exists...")
cur.execute("DROP TABLE IF EXISTS tradable_stocks")

print("✓ Database connection established")
print("✓ Existing 'tradable_stocks' table dropped (if existed)")
print(f"✓ Database file: {pathlib.Path(DB_FILE).absolute()}")

# Commit the changes (for the DROP TABLE operation)
con.commit()

print("\nDatabase setup complete. Connection and cursor are ready for use.")
print("Variables available:")
print(f"  - con: sqlite3.Connection object")
print(f"  - cur: sqlite3.Cursor object")
