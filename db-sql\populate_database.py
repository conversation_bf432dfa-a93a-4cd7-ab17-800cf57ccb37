import pandas as pd
import sqlite3
import re
from pathlib import Path

def normalize_column_name(col_name):
    """Convert column name to snake_case format"""
    # Strip whitespace
    col_name = col_name.strip()
    # Replace spaces and special characters with underscores
    col_name = re.sub(r'[\s\-\.\&]+', '_', col_name)
    # Convert to lowercase
    col_name = col_name.lower()
    # Remove multiple underscores
    col_name = re.sub(r'_+', '_', col_name)
    # Remove leading/trailing underscores
    col_name = col_name.strip('_')
    return col_name

def populate_database():
    """Load CSV data and populate the tradable_stocks database"""
    print("=== Populating Tradable Stocks Database ===")
    print()
    
    # Try different CSV file locations
    csv_files = [
        "sample_with_dates.csv",
        r"C:\Users\<USER>\.cache\nse_analyzer\nifty_500_stocks.csv",
        r"C:\Users\<USER>\sample_with_dates.csv"
    ]
    
    df = None
    csv_used = None
    
    # Find and load CSV file
    for csv_path in csv_files:
        if Path(csv_path).exists():
            print(f"Found CSV file: {csv_path}")
            try:
                # Load the CSV into DataFrame
                df = pd.read_csv(csv_path)
                csv_used = csv_path
                break
            except Exception as e:
                print(f"Error loading {csv_path}: {e}")
                continue
    
    if df is None:
        print("No CSV file found. Cannot populate database.")
        return False
    
    print(f"Successfully loaded CSV from: {csv_used}")
    print(f"Original shape: {df.shape}")
    
    # Strip and normalize column names to snake_case
    original_columns = df.columns.tolist()
    df.columns = [normalize_column_name(col) for col in df.columns]
    
    print("\nColumn name transformations:")
    for orig, new in zip(original_columns, df.columns):
        if orig != new:
            print(f"  '{orig}' -> '{new}'")
    
    # Convert date columns to datetime if they exist
    date_columns = [col for col in df.columns if 'date_of_listing' in col.lower()]
    if date_columns:
        for date_col in date_columns:
            print(f"\nConverting '{date_col}' to datetime...")
            df[date_col] = pd.to_datetime(df[date_col])
            # Convert to string format for SQLite storage
            df[date_col] = df[date_col].dt.strftime('%Y-%m-%d')
    
    # Connect to database
    try:
        conn = sqlite3.connect('tradable_stocks.db')
        cursor = conn.cursor()
        
        # Clear existing data
        cursor.execute("DELETE FROM tradable_stocks")
        print("\nCleared existing data from tradable_stocks table")
        
        # Insert data into database
        print(f"\nInserting {len(df)} records into database...")
        
        # Use pandas to_sql for easy insertion
        df.to_sql('tradable_stocks', conn, if_exists='append', index=False)
        
        # Verify insertion
        cursor.execute("SELECT COUNT(*) FROM tradable_stocks")
        count = cursor.fetchone()[0]
        
        print(f"✓ Successfully inserted {count} records into database")
        
        # Show sample of inserted data
        cursor.execute("SELECT * FROM tradable_stocks LIMIT 5")
        sample_data = cursor.fetchall()
        
        print("\nSample of inserted data:")
        print("-" * 80)
        cursor.execute("PRAGMA table_info(tradable_stocks)")
        columns = [col[1] for col in cursor.fetchall()]
        
        for row in sample_data:
            for col_name, value in zip(columns, row):
                print(f"{col_name}: {value}")
            print("-" * 40)
        
        conn.commit()
        conn.close()
        
        print("\n✓ Database population completed successfully!")
        return True
        
    except Exception as e:
        print(f"\nError populating database: {e}")
        if 'conn' in locals():
            conn.close()
        return False

if __name__ == "__main__":
    success = populate_database()
    if not success:
        print("\n✗ Failed to populate database")
    input("\nPress Enter to exit...")
